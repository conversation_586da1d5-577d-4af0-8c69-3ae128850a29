#!/bin/bash

# AI红警世界杯 2025 - 完整更新脚本
# 此脚本会强制更新所有组件：游戏引擎、源码、文档

set -e  # 遇到错误时退出

echo "🚀 AI红警世界杯 2025 - 完整更新..."
echo "=================================================="
echo "⚠️  警告：此操作将强制覆盖所有本地修改！"
echo "📝 如果您有重要的本地修改，请先备份"
echo ""

# 询问用户确认
read -p "是否继续？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 1
fi

echo ""
echo "🔄 开始强制更新所有组件..."

# 创建必要的目录
echo "📁 创建项目目录结构..."
mkdir -p downloads
mkdir -p engines

# 1. 询问是否强制更新 hackathon-code
echo ""
echo "📚 是否强制更新比赛文档和示例？"
echo "   这将删除现有的 hackathon-code 目录并重新下载"
read -p "强制更新 hackathon-code？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📚 强制更新比赛文档和示例..."
    if [ -d "hackathon-code" ]; then
        echo "🗑️  删除现有 hackathon-code 目录..."
        rm -rf hackathon-code
    fi

    echo "📥 克隆最新的比赛文档..."
    git clone https://github.com/OpenCodeAlert/Hackathon2025.git hackathon-code
    if [ $? -eq 0 ]; then
        echo "✅ hackathon-code 更新完成"
        # 删除 .git 目录，使其成为普通目录
        rm -rf hackathon-code/.git
    else
        echo "❌ hackathon-code 更新失败"
        exit 1
    fi
else
    echo "⏭️  跳过 hackathon-code 更新"
fi

# 2. 询问是否强制更新 copilot-source
echo ""
echo "🔧 是否强制更新比赛版本源码？"
echo "   这将删除现有的 copilot-source 目录并重新下载"
read -p "强制更新 copilot-source？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔧 强制更新比赛版本源码..."
    if [ -d "copilot-source" ]; then
        echo "🗑️  删除现有 copilot-source 目录..."
        rm -rf copilot-source
    fi

    echo "📥 克隆最新的比赛版本源码..."
    git clone https://github.com/OpenCodeAlert/OpenCodeAlert.git copilot-source
    if [ $? -eq 0 ]; then
        echo "✅ copilot-source 更新完成"
        # 删除 .git 目录，使其成为普通目录
        rm -rf copilot-source/.git
    
    # 询问是否恢复比赛任务配置
    echo ""
    echo "🎯 是否恢复比赛任务配置？"
    echo "   这将创建 missions.yaml 文件并修改 mod.yaml 以显示5个比赛任务"
    read -p "恢复比赛任务配置？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🎯 恢复比赛任务配置..."
        cat > copilot-source/mods/copilot/missions.yaml << 'EOF'
AI word cup 2025 missions:
	copilot-01
	copilot-02
	copilot-03
	copilot-04
	copilot-05
EOF

        # 修改 mod.yaml 使用 copilot 任务
        sed -i '' 's|ra|missions\.yaml|copilot|missions.yaml|g' copilot-source/mods/copilot/mod.yaml

        echo "✅ 比赛任务配置已恢复"
        else
            echo "⏭️  跳过比赛任务配置恢复"
        fi
    else
        echo "❌ copilot-source 更新失败"
        exit 1
    fi
else
    echo "⏭️  跳过 copilot-source 更新"
fi

# 3. 询问是否下载并更新游戏引擎
echo ""
echo "📦 是否下载并更新比赛引擎？"
echo "   这将下载最新的游戏引擎并覆盖现有的 engines/ 目录"
read -p "下载并更新游戏引擎？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📦 下载并更新比赛引擎..."
    cd downloads

    # 获取最新版本信息
    echo "🔍 获取最新版本信息..."
    LATEST_RELEASE=$(curl -s https://api.github.com/repos/OpenCodeAlert/Hackathon2025/releases/latest)
    if [ $? -ne 0 ]; then
        echo "⚠️  无法获取最新版本信息，使用默认版本"
        DOWNLOAD_URL="https://github.com/OpenCodeAlert/Hackathon2025/releases/download/v1.0.0/OpenCodeAlert-v1.0.0-MacOS_arm64.dmg"
        FILENAME="OpenCodeAlert-v1.0.0-MacOS_arm64.dmg"
    else
        # 检测系统架构
        if [[ $(uname -m) == "arm64" ]]; then
            ARCH="arm64"
        else
            ARCH="x64"
        fi

        # 提取下载链接
        DOWNLOAD_URL=$(echo "$LATEST_RELEASE" | grep -o "https://[^\"]*MacOS_${ARCH}[^\"]*\.dmg" | head -1)
        FILENAME=$(basename "$DOWNLOAD_URL")

        if [ -z "$DOWNLOAD_URL" ]; then
            echo "⚠️  无法找到适合的下载链接，使用默认版本"
            DOWNLOAD_URL="https://github.com/OpenCodeAlert/Hackathon2025/releases/download/v1.0.0/OpenCodeAlert-v1.0.0-MacOS_arm64.dmg"
            FILENAME="OpenCodeAlert-v1.0.0-MacOS_arm64.dmg"
        fi
    fi

    echo "⬇️  强制下载最新引擎: $FILENAME"

    # 删除旧文件并重新下载
    if [ -f "$FILENAME" ]; then
        rm "$FILENAME"
    fi

    curl -L -o "$FILENAME" "$DOWNLOAD_URL"
    if [ $? -ne 0 ]; then
        echo "❌ 下载失败"
        exit 1
    fi

    # 清空并重新安装引擎（只有在确认下载后才删除）
    echo "🗑️  清空现有引擎目录..."
    rm -rf ../engines/*

    # 处理 DMG 文件
    if [[ "$FILENAME" == *.dmg ]]; then
        echo "📂 挂载 DMG 文件..."
        MOUNT_OUTPUT=$(hdiutil attach "$FILENAME" 2>/dev/null || echo "")

        if [ -n "$MOUNT_OUTPUT" ]; then
            MOUNT_POINT=$(echo "$MOUNT_OUTPUT" | grep -o '/Volumes/[^[:space:]]*' | tail -1)
            echo "📋 挂载点: $MOUNT_POINT"

            # 查找并复制应用程序到 engines 目录
            APP_FOUND=false

            # 方法1: 直接在挂载点查找 .app
            for app in "$MOUNT_POINT"/*.app; do
                if [ -d "$app" ]; then
                    echo "📦 安装应用程序: $(basename "$app")"
                    cp -R "$app" ../engines/
                    APP_FOUND=true
                    break
                fi
            done

            # 方法2: 在子目录中查找 .app
            if [ "$APP_FOUND" = false ]; then
                for app in "$MOUNT_POINT"/*/*.app; do
                    if [ -d "$app" ]; then
                        echo "📦 安装应用程序: $(basename "$app")"
                        cp -R "$app" ../engines/
                        APP_FOUND=true
                        break
                    fi
                done
            fi

            if [ "$APP_FOUND" = false ]; then
                echo "⚠️  未找到应用程序文件"
                ls -la "$MOUNT_POINT"
            fi

            # 卸载 DMG
            echo "💿 卸载 DMG..."
            hdiutil detach "$MOUNT_POINT" 2>/dev/null || echo "⚠️  DMG 卸载失败，可能已经卸载"

            if [ "$APP_FOUND" = true ]; then
                echo "✅ 游戏引擎更新完成"
            else
                echo "❌ 游戏引擎安装失败"
                exit 1
            fi
        else
            echo "❌ DMG 挂载失败"
            exit 1
        fi
    fi

    cd ..
else
    echo "⏭️  跳过游戏引擎下载更新"
fi

# 4. 检查系统依赖
echo ""
echo "🔧 检查系统依赖..."

# 检查 Python
if command -v python3 &> /dev/null; then
    echo "✅ Python 已安装: $(python3 --version)"
else
    echo "❌ Python 未安装，请使用 brew install python 安装"
fi

# 检查 Git
if command -v git &> /dev/null; then
    echo "✅ Git 已安装: $(git --version)"
else
    echo "❌ Git 未安装，请使用 brew install git 安装"
fi

# 检查 .NET（用于编译源码）
if command -v dotnet &> /dev/null; then
    echo "✅ .NET 已安装: $(dotnet --version)"
elif [ -f "$HOME/.dotnet/dotnet" ]; then
    echo "✅ .NET 已安装: $($HOME/.dotnet/dotnet --version)"
else
    echo "⚠️  .NET 未安装"
    echo "如需编译源码，请运行: curl -sSL https://dot.net/v1/dotnet-install.sh | bash /dev/stdin --channel 6.0"
fi

# 完成
echo ""
echo "=================================================="
echo "✅ 所有组件强制更新完成！"
echo ""
echo "📁 项目结构："
echo "  ├── downloads/          # 下载的资源包"
echo "  ├── engines/            # 比赛版本游戏引擎（已更新）"
echo "  ├── hackathon-code/     # 比赛文档和示例（已更新）"
echo "  ├── copilot-source/     # 比赛版本源码（已更新）"
echo "  └── start-game.sh       # 比赛版本启动脚本"
echo ""
echo "🎮 启动比赛版本: ./start-game.sh"
echo "📚 查看比赛文档: cd hackathon-code"
echo "🔧 编译比赛源码: cd copilot-source && export PATH=\"\$HOME/.dotnet:\$PATH\" && make"
echo "🎮 从源码启动: cd copilot-source && export PATH=\"\$HOME/.dotnet:\$PATH\" && ./launch-game.sh Game.Mod=copilot"
echo ""
echo "🏆 祝您在 AI红警世界杯 2025 中取得好成绩！"
