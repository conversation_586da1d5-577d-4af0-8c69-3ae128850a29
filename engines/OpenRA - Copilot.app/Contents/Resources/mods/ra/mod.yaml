Metadata:
	Title: Red Alert
	Version: {DEV_VERSION}
	Website: https://www.openra.net
	WebIcon32: https://www.openra.net/images/icons/ra_32x32.png
	WindowTitle: OpenRA - Red Alert

PackageFormats: Mix

Packages:
	~^SupportDir|Content/ra/v2/
	~^SupportDir|Content/ra/v2/expand
	~^SupportDir|Content/ra/v2/cnc
	~^SupportDir|Content/ra/v2/movies
	^EngineDir
	$ra: ra
	^EngineDir|mods/common: common
	~main.mix
	~conquer.mix
	~lores.mix: lores
	~hires.mix
	~local.mix
	~sounds.mix
	~speech.mix
	~allies.mix
	~russian.mix
	~temperat.mix
	~snow.mix
	~interior.mix
	~scores.mix
	~expand2.mix
	~hires1.mix
	~desert.mix
	~general.mix
	ra|bits
	ra|bits/desert
	ra|scripts
	common|scripts
	ra|uibits

MapFolders:
	ra|maps: System
	~^SupportDir|maps/ra/{DEV_VERSION}: User

Rules:
	ra|rules/misc.yaml
	ra|rules/ai.yaml
	ra|rules/player.yaml
	ra|rules/palettes.yaml
	ra|rules/world.yaml
	ra|rules/defaults.yaml
	ra|rules/vehicles.yaml
	ra|rules/husks.yaml
	ra|rules/structures.yaml
	ra|rules/infantry.yaml
	ra|rules/civilian.yaml
	ra|rules/decoration.yaml
	ra|rules/aircraft.yaml
	ra|rules/ships.yaml
	ra|rules/fakes.yaml

Sequences:
	ra|sequences/ships.yaml
	ra|sequences/vehicles.yaml
	ra|sequences/structures.yaml
	ra|sequences/infantry.yaml
	ra|sequences/aircraft.yaml
	ra|sequences/misc.yaml
	ra|sequences/decorations.yaml

TileSets:
	ra|tilesets/snow.yaml
	ra|tilesets/interior.yaml
	ra|tilesets/temperat.yaml
	ra|tilesets/desert.yaml

Cursors:
	ra|cursors.yaml

Chrome:
	ra|chrome.yaml

Assemblies:
	^BinDir|OpenRA.Mods.Common.dll
	^BinDir|OpenRA.Mods.Cnc.dll

ChromeLayout:
	common|chrome/ingame.yaml
	common|chrome/ingame-chat.yaml
	common|chrome/ingame-transients.yaml
	common|chrome/ingame-fmvplayer.yaml
	common|chrome/ingame-info.yaml
	common|chrome/ingame-infoscripterror.yaml
	common|chrome/ingame-infobriefing.yaml
	common|chrome/ingame-infoobjectives.yaml
	common|chrome/ingame-infostats.yaml
	common|chrome/ingame-info-lobby-options.yaml
	common|chrome/ingame-menu.yaml
	ra|chrome/ingame-observer.yaml
	ra|chrome/ingame-player.yaml
	common|chrome/ingame-perf.yaml
	common|chrome/ingame-debug.yaml
	common|chrome/ingame-debug-hpf.yaml
	common|chrome/ingame-debuginfo.yaml
	common|chrome/ingame-infochat.yaml
	common|chrome/mainmenu.yaml
	common|chrome/mainmenu-prompts.yaml
	common|chrome/settings.yaml
	common|chrome/settings-display.yaml
	common|chrome/settings-audio.yaml
	common|chrome/settings-input.yaml
	common|chrome/settings-hotkeys.yaml
	common|chrome/settings-advanced.yaml
	common|chrome/credits.yaml
	common|chrome/lobby.yaml
	common|chrome/lobby-mappreview.yaml
	common|chrome/lobby-players.yaml
	common|chrome/lobby-options.yaml
	common|chrome/lobby-music.yaml
	common|chrome/lobby-servers.yaml
	common|chrome/lobby-kickdialogs.yaml
	common|chrome/color-picker.yaml
	common|chrome/map-chooser.yaml
	common|chrome/multiplayer-browser.yaml
	common|chrome/multiplayer-browserpanels.yaml
	common|chrome/multiplayer-createserver.yaml
	common|chrome/multiplayer-directconnect.yaml
	common|chrome/connection.yaml
	common|chrome/replaybrowser.yaml
	common|chrome/gamesave-browser.yaml
	ra|chrome/gamesave-loading.yaml
	common|chrome/dropdowns.yaml
	common|chrome/musicplayer.yaml
	common|chrome/tooltips.yaml
	common|chrome/assetbrowser.yaml
	common|chrome/missionbrowser.yaml
	common|chrome/confirmation-dialogs.yaml
	common|chrome/editor.yaml
	common|chrome/playerprofile.yaml
	common|chrome/text-notifications.yaml

Translations:
	common|languages/en.ftl
	common|languages/chrome/en.ftl
	common|languages/rules/en.ftl
	ra|languages/chrome/en.ftl
	ra|languages/rules/en.ftl

Weapons:
	ra|weapons/explosions.yaml
	ra|weapons/ballistics.yaml
	ra|weapons/missiles.yaml
	ra|weapons/other.yaml
	ra|weapons/smallcaliber.yaml
	ra|weapons/superweapons.yaml

Voices:
	ra|audio/voices.yaml

Notifications:
	ra|audio/notifications.yaml

Music:
	ra|audio/music.yaml

Hotkeys:
	common|hotkeys/game.yaml
	common|hotkeys/observer.yaml
	common|hotkeys/production-common.yaml
	common|hotkeys/supportpowers.yaml
	common|hotkeys/viewport.yaml
	common|hotkeys/chat.yaml
	common|hotkeys/editor.yaml
	common|hotkeys/control-groups.yaml
	ra|hotkeys.yaml

LoadScreen: LogoStripeLoadScreen
	Image: ra|uibits/loadscreen.png
	Image2x: ra|uibits/loadscreen-2x.png
	Image3x: ra|uibits/loadscreen-3x.png
	Text: Filling Crates..., Charging Capacitors..., Reticulating Splines..., Planting Trees..., Building Bridges..., Aging Empires..., Compiling EVA..., Constructing Pylons..., Activating Skynet..., Splitting Atoms...

ServerTraits:
	LobbyCommands
	SkirmishLogic
	PlayerPinger
	MasterServerPinger
	LobbySettingsNotification

ChromeMetrics:
	common|metrics.yaml
	ra|metrics.yaml

Fonts:
	Tiny:
		Font: common|FreeSans.ttf
		Size: 10
		Ascender: 8
	TinyBold:
		Font: common|FreeSansBold.ttf
		Size: 10
		Ascender: 8
	Small:
		Font: common|FreeSans.ttf
		Size: 12
		Ascender: 9
	Regular:
		Font: common|FreeSans.ttf
		Size: 14
		Ascender: 11
	Bold:
		Font: common|FreeSansBold.ttf
		Size: 14
		Ascender: 11
	MediumBold:
		Font: common|FreeSansBold.ttf
		Size: 18
		Ascender: 14
	BigBold:
		Font: common|FreeSansBold.ttf
		Size: 24
		Ascender: 18
	Title:
		Font: ra|ZoodRangmah.ttf
		Size: 48
		Ascender: 26

Missions:
	ra|missions.yaml

MapGrid:
	TileSize: 24,24
	Type: Rectangular

DefaultOrderGenerator: UnitOrderGenerator

SupportsMapsFrom: ra

SoundFormats: Aud, Wav

SpriteFormats: ShpD2, ShpTD, TmpRA, TmpTD, ShpTS

VideoFormats: Vqa, Wsa

TerrainFormat: DefaultTerrain

SpriteSequenceFormat: ClassicTilesetSpecificSpriteSequence

AssetBrowser:
	SpriteExtensions: .shp, .tmp, .tem, .des, .sno, .int
	AudioExtensions: .aud, .wav, .r00, .r01, .r02, .r03, .v00, .v01, .v02, .v03
	VideoExtensions: .vqa, .wsa

GameSpeeds:
	DefaultSpeed: default
	Speeds:
		slowest:
			Name: options-game-speed.slowest
			Timestep: 80
			OrderLatency: 2
		slower:
			Name: options-game-speed.slower
			Timestep: 50
			OrderLatency: 3
		default:
			Name: options-game-speed.normal
			Timestep: 40
			OrderLatency: 3
		fast:
			Name: options-game-speed.fast
			Timestep: 35
			OrderLatency: 4
		faster:
			Name: options-game-speed.faster
			Timestep: 30
			OrderLatency: 4
		fastest:
			Name: options-game-speed.fastest
			Timestep: 20
			OrderLatency: 6

ModContent:
	InstallPromptMessage: Red Alert requires artwork and audio from the original game.\n\nQuick Install will automatically download this content (without music\nor videos) from a mirror of the 2008 Red Alert freeware release.\n\nAdvanced Install includes options for copying the music, videos, and\nother content from an original game disc or digital installation.
	QuickDownload: quickinstall
	HeaderMessage: Game content may be extracted from the original game discs or an\nexisting digital install. OpenRA can also download the base game\nfiles from an online mirror of the 2008 freeware release of RA.
	Packages:
		ContentPackage@base:
			Title: Base Game Files
			Identifier: base
			TestFiles: ^SupportDir|Content/ra/v2/allies.mix, ^SupportDir|Content/ra/v2/conquer.mix, ^SupportDir|Content/ra/v2/interior.mix, ^SupportDir|Content/ra/v2/hires.mix, ^SupportDir|Content/ra/v2/lores.mix, ^SupportDir|Content/ra/v2/local.mix, ^SupportDir|Content/ra/v2/speech.mix, ^SupportDir|Content/ra/v2/russian.mix, ^SupportDir|Content/ra/v2/snow.mix, ^SupportDir|Content/ra/v2/sounds.mix, ^SupportDir|Content/ra/v2/temperat.mix
			Sources: allied, soviet, tfd, ra-steam, ra-origin, cncr-steam, cncr-origin
			Required: true
			Download: basefiles
		ContentPackage@aftermathbase:
			Title: Aftermath Expansion Files
			Identifier: aftermathbase
			TestFiles: ^SupportDir|Content/ra/v2/expand/expand2.mix, ^SupportDir|Content/ra/v2/expand/hires1.mix, ^SupportDir|Content/ra/v2/expand/lores1.mix, ^SupportDir|Content/ra/v2/expand/chrotnk1.aud, ^SupportDir|Content/ra/v2/expand/fixit1.aud, ^SupportDir|Content/ra/v2/expand/jburn1.aud, ^SupportDir|Content/ra/v2/expand/jchrge1.aud, ^SupportDir|Content/ra/v2/expand/jcrisp1.aud, ^SupportDir|Content/ra/v2/expand/jdance1.aud, ^SupportDir|Content/ra/v2/expand/jjuice1.aud, ^SupportDir|Content/ra/v2/expand/jjump1.aud, ^SupportDir|Content/ra/v2/expand/jlight1.aud, ^SupportDir|Content/ra/v2/expand/jpower1.aud, ^SupportDir|Content/ra/v2/expand/jshock1.aud, ^SupportDir|Content/ra/v2/expand/jyes1.aud, ^SupportDir|Content/ra/v2/expand/madchrg2.aud, ^SupportDir|Content/ra/v2/expand/madexplo.aud, ^SupportDir|Content/ra/v2/expand/mboss1.aud, ^SupportDir|Content/ra/v2/expand/mhear1.aud, ^SupportDir|Content/ra/v2/expand/mhotdig1.aud, ^SupportDir|Content/ra/v2/expand/mhowdy1.aud, ^SupportDir|Content/ra/v2/expand/mhuh1.aud, ^SupportDir|Content/ra/v2/expand/mlaff1.aud, ^SupportDir|Content/ra/v2/expand/mrise1.aud, ^SupportDir|Content/ra/v2/expand/mwrench1.aud, ^SupportDir|Content/ra/v2/expand/myeehaw1.aud, ^SupportDir|Content/ra/v2/expand/myes1.aud
			Sources: aftermath, tfd, ra-steam, ra-origin, cncr-steam, cncr-origin
			Required: true
			Download: aftermath
		ContentPackage@cncdesert:
			Title: C&C Desert Tileset
			Identifier: cncdesert
			TestFiles: ^SupportDir|Content/ra/v2/cnc/desert.mix
			Sources: tfd, cnc-steam, cnc-origin, cnc95, cncr-steam, cncr-origin
			Required: true
			Download: cncdesert
		ContentPackage@music:
			Title: Base Game Music
			Identifier: music
			TestFiles: ^SupportDir|Content/ra/v2/scores.mix
			Sources: allied, soviet, tfd, ra-steam, ra-origin, cncr-steam, cncr-origin
		ContentPackage@movies-allied:
			Title: Allied Campaign Briefings
			Identifier: movies-allied
			TestFiles: ^SupportDir|Content/ra/v2/movies/aagun.vqa, ^SupportDir|Content/ra/v2/movies/aftrmath.vqa, ^SupportDir|Content/ra/v2/movies/ally1.vqa, ^SupportDir|Content/ra/v2/movies/ally10.vqa, ^SupportDir|Content/ra/v2/movies/ally10b.vqa, ^SupportDir|Content/ra/v2/movies/ally11.vqa, ^SupportDir|Content/ra/v2/movies/ally12.vqa, ^SupportDir|Content/ra/v2/movies/ally14.vqa, ^SupportDir|Content/ra/v2/movies/ally2.vqa, ^SupportDir|Content/ra/v2/movies/ally4.vqa, ^SupportDir|Content/ra/v2/movies/ally5.vqa, ^SupportDir|Content/ra/v2/movies/ally6.vqa, ^SupportDir|Content/ra/v2/movies/ally8.vqa, ^SupportDir|Content/ra/v2/movies/ally9.vqa, ^SupportDir|Content/ra/v2/movies/allyend.vqa, ^SupportDir|Content/ra/v2/movies/allymorf.vqa, ^SupportDir|Content/ra/v2/movies/apcescpe.vqa, ^SupportDir|Content/ra/v2/movies/assess.vqa, ^SupportDir|Content/ra/v2/movies/battle.vqa, ^SupportDir|Content/ra/v2/movies/binoc.vqa, ^SupportDir|Content/ra/v2/movies/bmap.vqa, ^SupportDir|Content/ra/v2/movies/brdgtilt.vqa, ^SupportDir|Content/ra/v2/movies/crontest.vqa, ^SupportDir|Content/ra/v2/movies/cronfail.vqa, ^SupportDir|Content/ra/v2/movies/destroyr.vqa, ^SupportDir|Content/ra/v2/movies/dud.vqa, ^SupportDir|Content/ra/v2/movies/elevator.vqa, ^SupportDir|Content/ra/v2/movies/flare.vqa, ^SupportDir|Content/ra/v2/movies/frozen.vqa, ^SupportDir|Content/ra/v2/movies/grvestne.vqa, ^SupportDir|Content/ra/v2/movies/landing.vqa, ^SupportDir|Content/ra/v2/movies/masasslt.vqa, ^SupportDir|Content/ra/v2/movies/mcv.vqa, ^SupportDir|Content/ra/v2/movies/mcv_land.vqa, ^SupportDir|Content/ra/v2/movies/montpass.vqa, ^SupportDir|Content/ra/v2/movies/oildrum.vqa, ^SupportDir|Content/ra/v2/movies/overrun.vqa, ^SupportDir|Content/ra/v2/movies/prolog.vqa, ^SupportDir|Content/ra/v2/movies/redintro.vqa, ^SupportDir|Content/ra/v2/movies/shipsink.vqa, ^SupportDir|Content/ra/v2/movies/shorbom1.vqa, ^SupportDir|Content/ra/v2/movies/shorbom2.vqa, ^SupportDir|Content/ra/v2/movies/shorbomb.vqa, ^SupportDir|Content/ra/v2/movies/snowbomb.vqa, ^SupportDir|Content/ra/v2/movies/soviet1.vqa, ^SupportDir|Content/ra/v2/movies/sovtstar.vqa, ^SupportDir|Content/ra/v2/movies/spy.vqa, ^SupportDir|Content/ra/v2/movies/tanya1.vqa, ^SupportDir|Content/ra/v2/movies/tanya2.vqa, ^SupportDir|Content/ra/v2/movies/toofar.vqa, ^SupportDir|Content/ra/v2/movies/trinity.vqa
			Sources: allied, tfd, ra-steam, ra-origin, cncr-steam, cncr-origin
		ContentPackage@movies-soviet:
			Title: Soviet Campaign Briefings
			Identifier: movies-soviet
			TestFiles: ^SupportDir|Content/ra/v2/movies/aagun.vqa, ^SupportDir|Content/ra/v2/movies/cronfail.vqa, ^SupportDir|Content/ra/v2/movies/airfield.vqa, ^SupportDir|Content/ra/v2/movies/ally1.vqa, ^SupportDir|Content/ra/v2/movies/allymorf.vqa, ^SupportDir|Content/ra/v2/movies/averted.vqa, ^SupportDir|Content/ra/v2/movies/beachead.vqa, ^SupportDir|Content/ra/v2/movies/bmap.vqa, ^SupportDir|Content/ra/v2/movies/bombrun.vqa, ^SupportDir|Content/ra/v2/movies/countdwn.vqa, ^SupportDir|Content/ra/v2/movies/double.vqa, ^SupportDir|Content/ra/v2/movies/dpthchrg.vqa, ^SupportDir|Content/ra/v2/movies/execute.vqa, ^SupportDir|Content/ra/v2/movies/flare.vqa, ^SupportDir|Content/ra/v2/movies/landing.vqa, ^SupportDir|Content/ra/v2/movies/mcvbrdge.vqa, ^SupportDir|Content/ra/v2/movies/mig.vqa, ^SupportDir|Content/ra/v2/movies/movingin.vqa, ^SupportDir|Content/ra/v2/movies/mtnkfact.vqa, ^SupportDir|Content/ra/v2/movies/nukestok.vqa, ^SupportDir|Content/ra/v2/movies/onthprwl.vqa, ^SupportDir|Content/ra/v2/movies/periscop.vqa, ^SupportDir|Content/ra/v2/movies/prolog.vqa, ^SupportDir|Content/ra/v2/movies/radrraid.vqa, ^SupportDir|Content/ra/v2/movies/redintro.vqa, ^SupportDir|Content/ra/v2/movies/search.vqa, ^SupportDir|Content/ra/v2/movies/sfrozen.vqa, ^SupportDir|Content/ra/v2/movies/sitduck.vqa, ^SupportDir|Content/ra/v2/movies/slntsrvc.vqa, ^SupportDir|Content/ra/v2/movies/snowbomb.vqa, ^SupportDir|Content/ra/v2/movies/snstrafe.vqa, ^SupportDir|Content/ra/v2/movies/sovbatl.vqa, ^SupportDir|Content/ra/v2/movies/sovcemet.vqa, ^SupportDir|Content/ra/v2/movies/sovfinal.vqa, ^SupportDir|Content/ra/v2/movies/soviet1.vqa, ^SupportDir|Content/ra/v2/movies/soviet10.vqa, ^SupportDir|Content/ra/v2/movies/soviet11.vqa, ^SupportDir|Content/ra/v2/movies/soviet12.vqa, ^SupportDir|Content/ra/v2/movies/soviet13.vqa, ^SupportDir|Content/ra/v2/movies/soviet14.vqa, ^SupportDir|Content/ra/v2/movies/soviet2.vqa, ^SupportDir|Content/ra/v2/movies/soviet3.vqa, ^SupportDir|Content/ra/v2/movies/soviet4.vqa, ^SupportDir|Content/ra/v2/movies/soviet5.vqa, ^SupportDir|Content/ra/v2/movies/soviet6.vqa, ^SupportDir|Content/ra/v2/movies/soviet7.vqa, ^SupportDir|Content/ra/v2/movies/soviet8.vqa, ^SupportDir|Content/ra/v2/movies/soviet9.vqa, ^SupportDir|Content/ra/v2/movies/sovmcv.vqa, ^SupportDir|Content/ra/v2/movies/sovtstar.vqa, ^SupportDir|Content/ra/v2/movies/spotter.vqa, ^SupportDir|Content/ra/v2/movies/strafe.vqa, ^SupportDir|Content/ra/v2/movies/take_off.vqa, ^SupportDir|Content/ra/v2/movies/tesla.vqa, ^SupportDir|Content/ra/v2/movies/v2rocket.vqa
			Sources: soviet, tfd, ra-steam, ra-origin
		ContentPackage@music-counterstrike:
			Title: Counterstrike Music
			Identifier: music-counterstrike
			TestFiles: ^SupportDir|Content/ra/v2/expand/araziod.aud, ^SupportDir|Content/ra/v2/expand/backstab.aud, ^SupportDir|Content/ra/v2/expand/chaos2.aud, ^SupportDir|Content/ra/v2/expand/shut_it.aud, ^SupportDir|Content/ra/v2/expand/2nd_hand.aud, ^SupportDir|Content/ra/v2/expand/twinmix1.aud, ^SupportDir|Content/ra/v2/expand/under3.aud, ^SupportDir|Content/ra/v2/expand/vr2.aud,
			Sources: counterstrike, ra-steam, ra-origin, cncr-steam, cncr-origin
		ContentPackage@music-aftermath:
			Title: Aftermath Music
			Identifier: music-aftermath
			TestFiles: ^SupportDir|Content/ra/v2/expand/await.aud, ^SupportDir|Content/ra/v2/expand/bog.aud, ^SupportDir|Content/ra/v2/expand/float_v2.aud, ^SupportDir|Content/ra/v2/expand/gloom.aud, ^SupportDir|Content/ra/v2/expand/grndwire.aud, ^SupportDir|Content/ra/v2/expand/rpt.aud, ^SupportDir|Content/ra/v2/expand/search.aud, ^SupportDir|Content/ra/v2/expand/traction.aud, ^SupportDir|Content/ra/v2/expand/wastelnd.aud
			Sources: aftermath, ra-steam, ra-origin, cncr-steam, cncr-origin
	Downloads:
		ra|installer/downloads.yaml
	Sources:
		ra|installer/aftermath.yaml
		ra|installer/allies95.yaml
		ra|installer/cnc95.yaml
		ra|installer/counterstrike.yaml
		ra|installer/firstdecade.yaml
		ra|installer/origin.yaml
		ra|installer/soviet95.yaml
		ra|installer/steam.yaml

DiscordService:
	ApplicationId: 699222659766026240
