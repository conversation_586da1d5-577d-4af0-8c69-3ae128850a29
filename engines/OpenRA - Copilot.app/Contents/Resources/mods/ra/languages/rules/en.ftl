## player.yaml
options-tech-level =
    .infantry-only = Infantry Only
    .low = Low
    .medium = Medium
    .no-superweapons = No Superweapons
    .unrestricted = Unrestricted

checkbox-kill-bounties =
    .label = Kill Bounties
    .description = Players receive cash bonuses when killing enemy units

checkbox-redeployable-mcvs =
    .label = Redeployable MCVs
    .description = Allow undeploying Construction Yard

checkbox-reusable-engineers =
    .label = Reusable Engineers
    .description = Engineers remain on the battlefield after capturing a structure

notification-insufficient-funds = Insufficient funds.
notification-new-construction-options = New construction options.
notification-cannot-deploy-here = Cannot deploy here.
notification-low-power = Low power.
notification-base-under-attack = Base under attack.
notification-ally-under-attack = Our ally is under attack.
notification-silos-needed = Silos needed.

## world.yaml
options-starting-units =
    .mcv-only = MCV Only
    .light-support = Light Support
    .heavy-support = Heavy Support

resource-minerals = Valuable Minerals

## aircraft.yaml
actor-badr-name = Badger

actor-mig =
   .description = Fast Ground-Attack Plane.
      Strong vs Buildings, Vehicles
      Weak vs Infantry, Aircraft
   .name = MiG Attack Plane

actor-yak =
   .description = Attack Plane armed with
    dual machine guns.
      Strong vs Infantry, Light armor
      Weak vs Tanks, Aircraft
   .name = Yak Attack Plane

actor-tran =
   .description = Fast Infantry Transport Helicopter.
      Unarmed
   .name = Chinook

actor-heli =
   .description = Helicopter gunship armed
    with multi-purpose missiles.
      Strong vs Buildings, Vehicles, Aircraft
      Weak vs Infantry
   .name = Longbow

actor-hind =
   .description = Helicopter gunship armed
    with dual chainguns.
      Strong vs Infantry, Light armor
      Weak vs Tanks, Aircraft
   .name = Hind

actor-u2-name = Spy Plane

actor-mh60 =
   .description = Helicopter gunship armed
    with dual chainguns.
      Strong vs Infantry, Light armor
      Weak vs Tanks, Aircraft
   .name = Black Hawk

## civilian.yaml
actor-c10-name = Scientist
actor-tecn-name = Technician
actor-tecn2-name = Technician
actor-v01-name = Church
actor-v19-name = Oil Pump
actor-v19-husk-name = Husk (Oil Pump)
actor-barl-name = Explosive Barrel
actor-brl3-name = Explosive Barrel
actor-v25-name = Church
actor-lhus-name = Lighthouse
actor-windmill-name = Windmill

## decoration.yaml
actor-ice01-name = Ice Floe
actor-ice02-name = Ice Floe
actor-ice03-name = Ice Floe
actor-ice04-name = Ice Floe
actor-ice05-name = Ice Floe
actor-utilpol1-name = Utility Pole
actor-utilpol2-name = Utility Pole
actor-tanktrap1-name = Tank Trap
actor-tanktrap2-name = Tank Trap

## defaults.yaml
notification-unit-lost = Unit lost.
notification-airborne-unit-lost = Airborne Unit lost.
notification-naval-unit-lost = Naval Unit lost.
notification-unit-promoted = Unit promoted.
notification-primary-building-selected = Primary building selected.
notification-structure-captured = Structure captured.
notification-unit-stolen = Unit stolen.

meta-vehicle-generic-name = Vehicle
meta-infantry-generic-name = Soldier
meta-civinfantry-name = Civilian
meta-ship-generic-name = Ship
meta-neutralplane-generic-name = Plane
meta-helicopter-generic-name = Helicopter
meta-basicbuilding-generic-name = Structure
meta-techbuilding-name = Civilian Building
meta-ammobox-name = Ammo Box
meta-civfield-name = Field

meta-civhaystackorigloo =
   .winter--name = Igloo
   .summer--name = Haystacks

meta-tree-name = Tree
meta-treehusk-name = Tree (Burnt)
meta-box-name = Boxes
meta-husk-generic-name = Destroyed Vehicle
meta-planehusk-generic-name = Destroyed Plane
meta-helicopterhusk-generic-name = Destroyed Helicopter
meta-bridge-name = Bridge
meta-rock-name = Rock

meta-crate =
   .name = Crate
   .generic-name = Crate

meta-mine-name = Mine

## fakes.yaml
actor-fpwr =
   .description = Looks like a Power Plant.
   .name = Fake Power Plant
   .generic-name = Power Plant

actor-tenf =
   .description = Looks like an Allied Barracks.
   .name = Fake Allied Barracks
   .generic-name = Allied Barracks

actor-syrf =
   .description = Looks like a Naval Yard.
   .name = Fake Naval Yard
   .generic-name = Naval Yard

actor-spef =
   .description = Looks like a Sub Pen.
   .name = Fake Sub Pen
   .generic-name = Sub Pen

actor-weaf =
   .description = Looks like a War Factory.
   .name = Fake War Factory
   .generic-name = War Factory

actor-domf =
   .name = Fake Radar Dome
   .generic-name = Radar Dome
   .description = Looks like a Radar Dome.

actor-fixf =
   .description = Looks like a Service Depot.
   .name = Fake Service Depot
   .generic-name = Service Depot

actor-fapw =
   .description = Looks like an Advanced Power Plant.
   .name = Fake Advanced Power Plant
   .generic-name = Advanced Power Plant

actor-atef =
   .name = Fake Allied Tech Center
   .generic-name = Allied Tech Center
   .description = Looks like an Allied Tech Center.

actor-pdof =
   .name = Fake Chronosphere
   .generic-name = Chronosphere
   .description = Looks like a Chronosphere.
    Maximum 1 can be built.

actor-mslf =
   .name = Fake Missile Silo
   .generic-name = Missile Silo
   .description = Looks like a Missile Silo.
    Maximum 1 can be built.

actor-facf =
   .description = Looks like a Construction Yard.
   .name = Fake Construction Yard
   .generic-name = Construction Yard

## husks.yaml
actor-2tnk-husk-name = Husk (Medium Tank)
actor-3tnk-husk-name = Husk (Heavy Tank)
actor-4tnk-husk-name = Husk (Mammoth Tank)
actor-harv-fullhusk-name = Husk (Ore Truck)
actor-harv-emptyhusk-name = Husk (Ore Truck)
actor-mcv-husk-name = Husk (Mobile Construction Vehicle)
actor-mgg-husk-name = Husk (Mobile Gap Generator)
actor-tran-husk-name = Chinook
actor-tran-husk1-name = Husk (Chinook)
actor-tran-husk2-name = Husk (Chinook)
actor-badr-husk-name = Badger
actor-mig-husk-name = MiG Attack Plane
actor-yak-husk-name = Yak Attack Plane
actor-heli-husk-name = Longbow
actor-hind-husk-name = Hind
actor-u2-husk-name = Husk (Spy Plane)
actor-mh60-husk-name = Black Hawk

## infantry.yaml
notification-building-infiltrated = Building infiltrated.

actor-dog =
   .description = Anti-infantry unit.
    Can detect spies.
      Strong vs Infantry
      Weak vs Vehicles, Aircraft
   .name = Attack Dog
   .generic-name = Dog

actor-e1 =
   .description = General-purpose infantry.
      Strong vs Infantry
      Weak vs Vehicles, Aircraft
   .name = Rifle Infantry

actor-e2 =
   .description = Infantry armed with grenades.
      Strong vs Buildings, Infantry
      Weak vs Vehicles, Aircraft
   .name = Grenadier

actor-e3 =
   .description = Anti-tank/Anti-aircraft infantry.
      Strong vs Vehicles, Aircraft
      Weak vs Infantry
   .name = Rocket Soldier

actor-e4 =
   .description = Advanced anti-structure unit.
      Strong vs Infantry, Buildings
      Weak vs Vehicles, Aircraft
   .name = Flamethrower

actor-e6 =
   .description = Infiltrates and captures
    enemy structures.
      Unarmed
   .name = Engineer

actor-spy =
   .description = Infiltrates enemy structures for intel or
    sabotage. Exact effect depends on the
    building infiltrated.
    Loses disguise when attacking.
    Can detect spies.
      Strong vs Infantry
      Weak vs Vehicles, Aircraft
      Special Ability: Disguised
   .disguisetooltip-name = Spy
   .disguisetooltip-generic-name = Soldier

actor-spy-england-disguisetooltip-name = British Spy

actor-e7 =
   .description = Elite commando infantry. Armed with
    dual pistols and C4.
    Maximum 1 can be trained.
      Strong vs Infantry, Buildings
      Weak vs Vehicles, Aircraft
      Special Ability: Destroy Building with C4
   .name = Tanya

actor-medi =
   .description = Heals nearby infantry.
      Unarmed
   .name = Medic

actor-mech =
   .description = Repairs nearby vehicles and restores
    husks to working condition by capturing them.
      Unarmed
   .name = Mechanic

actor-einstein-name = Prof. Einstein
actor-delphi-name = Agent Delphi
actor-chan-name = Scientist
actor-gnrl-name = General

actor-thf =
   .description = Steals enemy credits.
    Hijacks enemy vehicles.
      Unarmed
   .name = Thief

actor-shok =
   .description = Elite infantry with portable Tesla coils.
      Strong vs Infantry, Vehicles
      Weak vs Aircraft
   .name = Shock Trooper

actor-zombie =
   .name = Zombie
   .description = Slow undead. Attacks in close combat.

actor-ant =
   .name = Giant Ant
   .generic-name = Ant
   .description = Irradiated insect that grew oversize.

actor-fireant-name = Fire Ant
actor-scoutant-name = Scout Ant
actor-warriorant-name = Warrior Ant

## misc.yaml
notification-sonar-pulse-ready = Sonar pulse ready.

actor-moneycrate-name = Money Crate
actor-healcrate-name = Heal Crate
actor-wcrate-name = Wooden Crate
actor-scrate-name = Steel Crate
actor-camera-name = (reveals area to owner)
actor-camera-paradrop-name = (support power proxy camera)
actor-camera-spyplane-name = (support power proxy camera)
actor-sonar-name = (support power proxy camera)
actor-flare-name = Flare
actor-mine-name = Ore Mine
actor-gmine-name = Gem Mine
actor-railmine-name = Abandoned Mine
actor-quee-name = Queen Ant
actor-lar1-name = Ant Larva
actor-lar2-name = Ant Larvae
actor-mpspawn-name = (multiplayer player starting point)
actor-waypoint-name = (waypoint for scripted behavior)
actor-ctflag-name = Flag

## ships.yaml
actor-ss =
   .description = Submerged anti-ship unit
    armed with torpedoes.
    Can detect other submarines.
      Strong vs Naval units
      Weak vs Ground units, Aircraft
      Special Ability: Submerge
   .name = Submarine

actor-msub =
   .description = Submerged anti-ground siege unit
    with anti-air capabilities.
    Can detect other submarines.
      Strong vs Buildings, Ground units, Aircraft
      Weak vs Naval units
      Special Ability: Submerge
   .name = Missile Submarine

actor-dd =
   .description = Fast multi-role ship.
    Can detect submarines.
      Strong vs Naval units, Vehicles, Aircraft
      Weak vs Infantry
   .name = Destroyer

actor-ca =
   .description = Very slow long-range ship.
      Strong vs Buildings, Ground units
      Weak vs Naval units, Aircraft
   .name = Cruiser

actor-lst =
   .description = General-purpose naval transport.
    Can carry infantry and tanks.
      Unarmed
   .name = Transport

actor-pt =
   .description = Light scout & support ship.
    Can detect submarines.
      Strong vs Naval units
      Weak vs Ground units, Aircraft
   .name = Gunboat

## structures.yaml
notification-construction-complete = Construction complete.
notification-unit-ready = Unit ready.
notification-unable-to-build-more = Unable to build more.
notification-unable-to-comply-building-in-progress = Unable to comply. Building in progress.
notification-repairing = Repairing.
notification-unit-repaired = Unit repaired.
notification-select-target = Select target.
notification-insufficient-power = Insufficient power.
notification-reinforcements-have-arrived = Reinforcements have arrived.
notification-abomb-prepping = A-bomb prepping.
notification-abomb-ready = A-bomb ready.
notification-abomb-launch-detected = A-bomb launch detected.
notification-iron-curtain-charging = Iron curtain charging.
notification-iron-curtain-ready = Iron curtain ready.
notification-chronosphere-charging = Chronosphere charging.
notification-chronosphere-ready = Chronosphere ready.
notification-satellite-launched = Satellite launched.
notification-credits-stolen = Credits stolen.
notifcation-spy-plane-ready = Spy plane ready.

actor-mslo =
   .name = Missile Silo
   .description = Provides an atomic bomb.
    Requires power to operate.
    Maximum 1 can be built.
      Special Ability: Atom Bomb
   .nukepower-name = Atom Bomb
   .nukepower-description = Launches a devastating atomic bomb
    at a target location.

actor-gap =
   .name = Gap Generator
   .description = Obscures the enemy's view with shroud.
    Requires power to operate.

actor-spen =
   .name = Sub Pen
   .description = Produces and repairs
    submarines and transports.

actor-syrd =
   .description = Produces and repairs
    ships and transports.
   .name = Naval Yard

actor-iron =
   .description = Makes a group of units invulnerable
    for a short time.
    Requires power to operate.
    Maximum 1 can be built.
      Special Ability: Invulnerability
   .name = Iron Curtain
   .grantexternalconditionpower-ironcurtain-name = Invulnerability
   .grantexternalconditionpower-ironcurtain-description = Makes a group of units invulnerable
    for 20 seconds.

actor-pdox =
   .description = Teleports a group of units across the
    map for a short time.
    Requires power to operate.
    Maximum 1 can be built.
      Special Ability: Chronoshift
   .name = Chronosphere
   .chronoshiftpower-chronoshift-name = Chronoshift
   .chronoshiftpower-chronoshift-description = Teleports a group of units across
    the map for 20 seconds.
   .chronoshiftpower-advancedchronoshift-name = Advanced Chronoshift
   .chronoshiftpower-advancedchronoshift-description = Teleports a large group of units across
    the map for 20 seconds.

actor-tsla =
   .description = Advanced base defense.
    Requires power to operate.
    Can detect cloaked units.
      Strong vs Vehicles, Infantry
      Weak vs Aircraft
   .name = Tesla Coil

actor-agun =
   .description = Anti-Air base defense.
    Requires power to operate.
      Strong vs Aircraft
      Weak vs Ground units
   .name = AA Gun

actor-dome =
   .description = Provides an overview
    of the battlefield.
    Requires power to operate.
   .name = Radar Dome

actor-pbox =
   .name = Pillbox
   .description = Static defense with a fireport for
    a garrisoned soldier.
    Can detect cloaked units.
      Strong vs Infantry, Light armor
      Weak vs Tanks, Aircraft

actor-hbox =
   .name = Camo Pillbox
   .description = Camouflaged static defense with a fireport
    for a garrisoned soldier.
    Can detect cloaked units.
      Strong vs Infantry, Light armor
      Weak vs Tanks, Aircraft

actor-gun =
   .description = Anti-Armor base defense.
    Can detect cloaked units.
      Strong vs Vehicles
      Weak vs Infantry, Aircraft
   .name = Turret

actor-ftur =
   .description = Anti-Infantry base defense.
    Can detect cloaked units.
      Strong vs Infantry, Light armor
      Weak vs Tanks, Aircraft
   .name = Flame Tower

actor-sam =
   .description = Anti-Air base defense.
    Requires power to operate.
      Strong vs Aircraft
      Weak vs Ground units
   .name = SAM Site

actor-atek =
   .description = Provides Allied advanced technologies.
      Special Ability: GPS Satellite
   .name = Allied Tech Center
   .gpspower-name = GPS Satellite
   .gpspower-description = Reveals map terrain and provides tactical
    information. Requires power and active radar.

actor-weap =
   .description = Produces vehicles.
   .name = War Factory

actor-fact =
   .description = Produces structures.
   .name = Construction Yard

actor-proc =
   .description = Refines Ore and Gems
    into credits.
   .name = Ore Refinery

actor-silo =
   .description = Stores excess refined
    Ore and Gems.
   .name = Silo

actor-hpad =
   .description = Produces and reloads
    helicopters.
   .name = Helipad

actor-afld =
   .description = Produces and reloads aircraft.
      Special Ability: Spy Plane
      Special Ability: Paratroopers
   .name = Airfield
   .airstrikepower-spyplane-name = Spy Plane
   .airstrikepower-spyplane-description = Reveals an area of the map.
   .paratrooperspower-paratroopers-name = Paratroopers
   .paratrooperspower-paratroopers-description = A Badger drops a squad of infantry
    at the selected location.
   .airstrikepower-parabombs-name = Parabombs
   .airstrikepower-parabombs-description = A Badger drops a load of parachuted bombs
    at the selected location.

actor-afld-ukraine-description = Produces and reloads aircraft.
      Special Ability: Spy Plane
      Special Ability: Paratroopers
      Special Ability: Parabombs

actor-powr =
   .description = Provides power for other structures.
   .name = Power Plant

actor-apwr =
   .description = Provides double the power of
    a standard Power Plant.
   .name = Advanced Power Plant

actor-stek =
   .description = Provides Soviet advanced technologies.
   .name = Soviet Tech Center

actor-barr =
   .description = Trains infantry.
   .name = Soviet Barracks

actor-kenn =
   .description = Trains Attack Dogs.
   .name = Kennel

actor-tent =
   .description = Trains infantry.
   .name = Allied Barracks

actor-fix =
   .description = Repairs vehicles for credits.
   .name = Service Depot

actor-sbag =
   .description = Stops infantry and light vehicles.
    Can be crushed by tanks.
   .name = Sandbag Wall

actor-fenc =
   .description = Stops infantry and light vehicles.
    Can be crushed by tanks.
   .name = Wire Fence

actor-brik =
   .description = Stop units and blocks enemy fire.
   .name = Concrete Wall

actor-cycl-name = Chain-Link Barrier
actor-barb-name = Barbed-Wire Fence
actor-wood-name = Wooden Fence
actor-barracks-name = Infantry Production
actor-techcenter-name = Tech Center
actor-anypower-name = Any Power Generation

## vehicles.yaml
actor-v2rl =
   .description = Long-range rocket artillery.
      Strong vs Infantry, Buildings
      Weak vs Vehicles, Aircraft
   .name = V2 Rocket Launcher

actor-1tnk =
   .description = Fast tank, good for scouting.
      Strong vs Light armor
      Weak vs Infantry, Tanks, Aircraft
   .name = Light Tank
   .generic-name = Tank

actor-2tnk =
   .description = Allied Main Battle Tank.
      Strong vs Vehicles
      Weak vs Infantry, Aircraft
   .name = Medium Tank
   .generic-name = Tank

actor-3tnk =
   .description = Soviet Main Battle Tank, with dual cannons
      Strong vs Vehicles
      Weak vs Infantry, Aircraft
   .name = Heavy Tank
   .generic-name = Tank

actor-4tnk =
   .description = Big and slow tank, with anti-air capability.
    Can crush concrete walls.
      Strong vs Vehicles, Infantry, Aircraft
      Weak vs Nothing
   .name = Mammoth Tank
   .generic-name = Tank

actor-arty =
   .description = Long-range artillery.
      Strong vs Infantry, Buildings
      Weak vs Vehicles, Aircraft
   .name = Artillery

actor-harv =
   .description = Collects Ore and Gems for processing.
      Unarmed
   .name = Ore Truck
   .generic-name = Harvester

actor-mcv =
   .description = Deploys into another Construction Yard.
      Unarmed
   .name = Mobile Construction Vehicle

actor-jeep =
   .description = Fast scout & anti-infantry vehicle.
    Can carry one infantry.
      Strong vs Infantry
      Weak vs Vehicles, Aircraft
   .name = Ranger

actor-apc =
   .description = Tough infantry transport.
      Strong vs Infantry, Light armor
      Weak vs Tanks, Aircraft
   .name = Armored Personnel Carrier

actor-mnly =
   .description = Lays mines to destroy
    unwary enemy units.
    Can detect mines.
      Unarmed
   .name = Minelayer

actor-truk =
   .description = Transports cash to other players.
      Unarmed
   .name = Supply Truck

actor-mgg =
   .description = Regenerates the shroud nearby,
    obscuring the area.
      Unarmed
   .name = Mobile Gap Generator

actor-mrj =
   .name = Mobile Radar Jammer
   .description = Jams nearby enemy radar domes
    and deflects incoming missiles.
      Unarmed

actor-ttnk =
   .description = Tank with mounted Tesla coil.
      Strong vs Infantry, Vehicles, Buildings
      Weak vs Aircraft
   .name = Tesla Tank
   .generic-name = Tank

actor-ftrk =
   .description = Mobile unit with mounted Flak cannon.
      Strong vs Infantry, Light armor, Aircraft
      Weak vs Tanks
   .name = Mobile Flak

actor-dtrk =
   .description = Truck with actively armed nuclear
    explosives. Has very weak armor.
   .name = Demolition Truck

actor-ctnk =
   .description = Armed with anti-ground missiles.
    Teleports to areas within range.
      Strong vs Vehicles, Buildings
      Weak vs Infantry, Aircraft
      Special ability: Can teleport
   .name = Chrono Tank
   .generic-name = Tank

actor-qtnk =
   .description = Deals seismic damage to nearby vehicles
    and structures.
      Strong vs Vehicles, Buildings
      Weak vs Infantry, Aircraft
   .name = MAD Tank
   .generic-name = Tank

actor-stnk =
   .description = Lightly armored infantry transport which
    can cloak. Armed with anti-ground missiles.
      Strong vs Light armor
      Weak vs Infantry, Tanks, Aircraft
   .name = Phase Transport

## Civilian Tech
actor-hosp =
   .name = Hospital
   .captured-desc = Provides infantry with self-healing.
   .capturable-desc = Capture to enable self-healing for infantry.

actor-fcom =
   .name = Forward Command
   .captured-desc = Provides buildable area.
   .capturable-desc =Capture to give buildable area.

actor-miss =
   .name = Communications Center
   .captured-desc = Provides range of vision.
   .capturable-desc = Capture to give visual range.

actor-bio =
   .name = Biological Lab
   .captured-desc = Provides prerequisite for Bio-Lab units.
   .capturable-desc = Capture to produce Bio-Lab units.

actor-oilb =
   .name = Oil Derrick
   .captured-desc = Provides additional funds.
   .capturable-desc =  Capture to receive additional funds.

## misc.yaml
actor-powerproxy-parabombs =
   .name = Parabombs (Single Use)
   .description = A Badger drops a load of parachuted bombs
    at the selected location.

actor-powerproxy-sonarpulse =
   .name = Sonar Pulse
   .description = Reveals all submarines in the vicinity for a
    short time.

actor-powerproxy-paratroopers =
   .name = Paratroopers
   .description = A Badger drops a squad of infantry
    anywhere on the map.
