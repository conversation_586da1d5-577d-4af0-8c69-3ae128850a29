fcom:
	Defaults:
		Filename: fcom.shp
	idle:
	damaged-idle:
		Start: 1
	make:
		Filename: fcommake.shp
		Length: *
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
		Length: *

hosp:
	Defaults:
		Filename: hosp.shp
	idle:
		Length: 4
	damaged-idle:
		Start: 4
		Length: 4
	dead:
		Start: 8
		Tick: 800
	make:
		Filename: hospmake.shp
		Length: *
	bib:
		TilesetFilenames:
			SNOW: mbHOSP.sno
			INTERIOR: mbHOSP.int
			TEMPERAT: mbHOSP.tem
			DESERT: mbHOSP.des
		Length: *
		Offset: 0,1
	icon:
		Filename: hospicon.shp

bio:
	Defaults:
		Filename: bio.shp
	idle:
	damaged-idle:
		Start: 1
	dead:
		Start: 2
		Tick: 800
	make:
		Filename: biomake.shp
		Length: *

oilb:
	Defaults:
		Filename: oilb.shp
		Offset: 0,-6
	idle:
	damaged-idle:
		Start: 1
		Length: *
	make:
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
		Length: *
		Offset: 0,0

fact:
	Defaults:
		Filename: fact.shp
	idle:
	make:
		Filename: factmake.shp
		Length: *
	build:
		Start: 1
		Length: 25
	damaged-idle:
		Start: 26
	damaged-build:
		Start: 27
		Length: 25
	dead:
		Filename: factdead.shp
		Tick: 800
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
		Length: *
	icon:
		Filename: facticon.shp
	fake-icon:
		Filename: facficon.shp

proc:
	Defaults:
		Filename: proc.shp
	idle:
		ZOffset: -1c511
	damaged-idle:
		Start: 1
		ZOffset: -1c511
	idle-top:
		Filename: proctop.shp
		ZOffset: 0
	damaged-idle-top:
		Filename: proctop.shp
		Start: 1
		ZOffset: 0
	make:
		Filename: procmake.shp
		Length: *
	dead:
		Filename: procdead.shp
		Tick: 800
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
		Length: *
	icon:
		Filename: procicon.shp

silo:
	idle:
		Filename: silo2.shp
		Offset: 0,-1
	damaged-idle:
		Filename: silo2.shp
		Start: 9
		Offset: 0,-1
	stages:
		Filename: silo2.shp
		Length: 9
		Offset: 0,-1
	damaged-stages:
		Filename: silo2.shp
		Start: 9
		Length: 9
		Offset: 0,-1
	make:
		Filename: silomake.shp
		Length: *
		Offset: 0,-1
	bib:
		TilesetFilenames:
			SNOW: mbSILO.sno
			INTERIOR: mbSILO.int
			TEMPERAT: mbSILO.tem
			DESERT: mbSILO.des
		Length: *
	icon:
		Filename: siloicon.shp

powr:
	Defaults:
		Filename: powr.shp
	idle:
	damaged-idle:
		Start: 1
	make:
		Filename: powrmake.shp
		Length: *
	dead:
		Filename: powrdead.shp
		Tick: 800
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
		Length: *
	icon:
		Filename: powricon.shp
	fake-icon:
		Filename: fpwricon.shp

apwr:
	Defaults:
		Filename: apwr.shp
	idle:
		Offset: 0,-10
	damaged-idle:
		Start: 1
		Offset: 0,-10
	make:
		Filename: apwrmake.shp
		Length: *
		Offset: 0,-10
	dead:
		Filename: apwrdead.shp
		Tick: 800
		Offset: 0,-10
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
		Length: *
	icon:
		Filename: apwricon.shp
	fake-icon:
		Filename: fapwicon.shp

barr:
	Defaults:
		Filename: barr.shp
	idle:
		Length: 10
		Offset: 0,-6
	damaged-idle:
		Start: 10
		Length: 10
		Offset: 0,-6
	make:
		Filename: barrmake.shp
		Length: *
		Offset: 0,-6
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
		Length: *
	icon:
		Filename: barricon.shp

tent:
	Defaults:
		Filename: tent.tem
		TilesetFilenames:
			SNOW: tent.sno
			DESERT: tent.des
	idle:
		Length: 10
	damaged-idle:
		Start: 10
		Length: 10
	make:
		Filename: tentmake.tem
		TilesetFilenames:
			SNOW: tentmake.sno
			DESERT: tentmake.des
		Length: *
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
		Length: *
	icon:
		Filename: tenticon.shp
		TilesetFilenames:
	fake-icon:
		Filename: tenficon.shp
		TilesetFilenames:

kenn:
	Defaults:
		Filename: kenn.shp
	idle:
	damaged-idle:
		Start: 1
	make:
		Filename: kennmake.shp
		Length: *
	bib:
		TilesetFilenames:
			SNOW: mbSILO.sno
			INTERIOR: mbSILO.int
			TEMPERAT: mbSILO.tem
			DESERT: mbSILO.des
		Length: *
	icon:
		Filename: kennicon.shp

dome:
	Defaults:
		Filename: dome.shp
	idle:
		Offset: 0,-4
	damaged-idle:
		Start: 1
		Offset: 0,-4
	make:
		Filename: domemake.shp
		Length: *
		Offset: 0,-4
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
		Length: *
	icon:
		Filename: domeicon.shp
	fake-icon:
		Filename: domficon.shp

atek:
	Defaults:
		Filename: atek.shp
	idle:
	damaged-idle:
		Start: 1
	make:
		Filename: atekmake.shp
		Length: *
	active:
		Filename: sputdoor.shp
		Length: *
		Offset: -4,0
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
		Length: *
	icon:
		Filename: atekicon.shp
	fake-icon:
		Filename: ateficon.shp

stek:
	Defaults:
		Filename: stek.shp
	idle:
	damaged-idle:
		Start: 1
	make:
		Filename: stekmake.shp
		Length: *
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
		Length: *
	icon:
		Filename: stekicon.shp

weap:
	Defaults:
		Filename: weap.shp
	idle:
	damaged-idle:
		Start: 1
	place:
		Filename: weapmake.shp
		Start: 14
	make:
		Filename: weapmake.shp
		Length: *
	build-top:
		Filename: weap3.shp
		Length: 10
	damaged-build-top:
		Filename: weap2.shp
		Start: 4
		Length: 4
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
		Length: *
	icon:
		Filename: weapicon.shp
	fake-icon:
		Filename: weaficon.shp

hpad:
	Defaults:
		Filename: hpad.shp
	idle:
		ZOffset: -1023
	damaged-idle:
		Start: 7
		ZOffset: -1023
	active:
		Start: 1
		Length: 6
		Tick: 100
		ZOffset: -1023
	damaged-active:
		Start: 8
		Length: 6
		Tick: 100
		ZOffset: -1023
	make:
		Filename: hpadmake.shp
		Length: *
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
		Length: *
	icon:
		Filename: hpadicon.shp

afld:
	Defaults:
		Filename: afld.shp
	idle:
		Filename: afldidle.shp
		Length: 8
		Tick: 160
		ZOffset: -1023
		Offset: 0,-4
	damaged-idle:
		Filename: afldidle.shp
		Start: 8
		Length: 8
		Tick: 160
		ZOffset: -1023
		Offset: 0,-4
	active:
		Length: 8
		Tick: 160
		ZOffset: -1023
		Offset: 0,-4
	damaged-active:
		Start: 8
		Length: 8
		Tick: 160
		ZOffset: -1023
		Offset: 0,-4
	make:
		Filename: afldmake.shp
		Length: *
		Offset: 0,-4
	icon:
		Filename: afldicon.shp

spen:
	Defaults:
		Filename: spen.shp
	idle:
		Offset: 0,2
	damaged-idle:
		Start: 1
		Offset: 0,2
	make:
		Filename: spenmake.shp
		Length: *
		Offset: 0,2
	icon:
		Filename: spenicon.shp
	fake-icon:
		Filename: speficon.shp

syrd:
	Defaults:
		Filename: syrd.shp
	idle:
	damaged-idle:
		Start: 1
	make:
		Filename: syrdmake.shp
		Length: *
	icon:
		Filename: syrdicon.shp
	fake-icon:
		Filename: syrficon.shp

fix:
	Defaults:
		Filename: fix.shp
	idle:
		Offset: 0,1
		ZOffset: -1c511
	damaged-idle:
		Start: 7
		Offset: 0,1
		ZOffset: -1c511
	active:
		Start: 1
		Length: 6
		Offset: 0,1
		ZOffset: -1c511
	damaged-active:
		Start: 8
		Length: 6
		Offset: 0,1
		ZOffset: -1c511
	make:
		Filename: fixmake.shp
		Length: *
		Offset: 0,1
	bib:
		TilesetFilenames:
			SNOW: mbFIX.sno
			INTERIOR: mbFIX.int
			TEMPERAT: mbFIX.tem
			DESERT: mbFIX.des
		Length: *
		ZOffset: -1c511
		Offset: 0,-4
	icon:
		Filename: fixicon.shp
	fake-icon:
		Filename: fixficon.shp

gun:
	Defaults:
		Filename: gun.shp
	idle: # Empty first frame. We need WithSpriteBody for the make anim, and WSB needs at least a placeholder default sequence to work
		Filename: gunmake.shp
	make:
		Filename: gunmake.shp
		Length: *
	turret:
		Facings: 32
		UseClassicFacings: True
	recoil:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	damaged-turret:
		Start: 64
		Facings: 32
		UseClassicFacings: True
	damaged-recoil:
		Start: 96
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	bib:
		TilesetFilenames:
			SNOW: mbGUN.sno
			INTERIOR: mbGUN.int
			TEMPERAT: mbGUN.tem
			DESERT: mbGUN.des
		Length: *
		Offset: -1,-1
	icon:
		Filename: gunicon.shp

agun:
	Defaults:
		Filename: agun.shp
	idle: # Empty first frame (agunmake has no empty frames). We need WithSpriteBody for the make anim, and WSB needs at least a placeholder default sequence to work
		Filename: gunmake.shp
	make:
		Filename: agunmake.shp
		Length: *
		Offset: 0,-13
	turret:
		Facings: 32
		UseClassicFacings: True
		Offset: 0,-13
	recoil:
		Start: 32
		Facings: 32
		UseClassicFacings: True
		Offset: 0,-13
	damaged-turret:
		Start: 64
		Facings: 32
		UseClassicFacings: True
		Offset: 0,-13
	damaged-recoil:
		Start: 96
		Facings: 32
		UseClassicFacings: True
		Offset: 0,-13
	muzzle:
		Filename: gunfire2.shp
		Start: 1
		Length: 4
	bib:
		TilesetFilenames:
			SNOW: mbAGUN.sno
			INTERIOR: mbAGUN.int
			TEMPERAT: mbAGUN.tem
			DESERT: mbAGUN.des
		Length: *
	icon:
		Filename: agunicon.shp

sam:
	idle: # Empty first frame (sammake has no empty frames). We need WithSpriteBody for the make anim, and WSB needs at least a placeholder default sequence to work
		Filename: gunmake.shp
	turret:
		Filename: sam2.shp
		Facings: 32
		UseClassicFacings: True
		Offset: -1,-2
	damaged-turret:
		Filename: sam2.shp
		Start: 34
		Facings: 32
		UseClassicFacings: True
		Offset: -1,-2
	make:
		Filename: sammake.shp
		Length: *
		Offset: -1,-2
	muzzle:
		Filename: samfire.shp
		Length: 18
		Facings: 8
		Offset: -1,6
	bib:
		TilesetFilenames:
			SNOW: mbSAM.sno
			INTERIOR: mbSAM.int
			TEMPERAT: mbSAM.tem
			DESERT: mbSAM.des
		Length: *
		Offset: 0,1
	icon:
		Filename: samicon.shp

ftur:
	Defaults:
		Filename: ftur.shp
	idle:
		Offset: 0,-2
	damaged-idle:
		Start: 1
		Offset: 0,-2
	make:
		Filename: fturmake.shp
		Length: *
		Offset: 0,-2
	bib:
		TilesetFilenames:
			SNOW: mbFTUR.sno
			INTERIOR: mbFTUR.int
			TEMPERAT: mbFTUR.tem
			DESERT: mbFTUR.des
		Length: *
	icon:
		Filename: fturicon.shp

tsla:
	Defaults:
		Filename: tsla.shp
	idle:
		Offset: 0,-13
	damaged-idle:
		Start: 10
		Offset: 0,-13
	make:
		Filename: tslamake.shp
		Length: *
		Offset: 0,-13
	active:
		Start: 1
		Length: 9
		Tick: 100
		Offset: 0,-13
	damaged-active:
		Start: 11
		Length: 9
		Tick: 100
		Offset: 0,-13
	bib:
		TilesetFilenames:
			SNOW: mbTSLA.sno
			INTERIOR: mbTSLA.int
			TEMPERAT: mbTSLA.tem
			DESERT: mbTSLA.des
		Length: *
	icon:
		Filename: tslaicon.shp

pbox:
	Defaults:
		Filename: pbox.shp
	idle:
	damaged-idle:
		Start: 1
	make:
		Filename: pboxmake.shp
		Length: *
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	bib:
		TilesetFilenames:
			SNOW: mbPBOX.sno
			INTERIOR: mbPBOX.int
			TEMPERAT: mbPBOX.tem
			DESERT: mbPBOX.des
		Length: *
		Offset: 0,-2
	icon:
		Filename: pboxicon.shp

hbox:
	Defaults:
		Filename: hbox.tem
		TilesetFilenames:
			SNOW: hbox.sno
			DESERT: hbox.des
	idle:
	damaged-idle:
		Start: 2
	make:
		Filename: hboxmake.tem
		TilesetFilenames:
			SNOW: hboxmake.sno
			DESERT: hboxmake.des
		Length: *
	muzzle:
		Filename: minigun.shp
		TilesetFilenames:
		Length: 6
		Facings: 8
	icon:
		Filename: hboxicon.shp
		TilesetFilenames:

gap:
	Defaults:
		Filename: gap.shp
	idle:
		Length: 32
		Offset: 0,-14
	damaged-idle:
		Start: 32
		Length: 32
		Offset: 0,-14
	make:
		Filename: gapmake.shp
		Length: *
		Offset: 0,-14
	bib:
		TilesetFilenames:
			SNOW: mbGAP.sno
			INTERIOR: mbGAP.int
			TEMPERAT: mbGAP.tem
			DESERT: mbGAP.des
		Length: *
	icon:
		Filename: gapicon.shp

iron:
	Defaults:
		Filename: iron.shp
	idle:
		Offset: 0,-4
	active:
		Length: 11
		Offset: 0,-4
	damaged-idle:
		Start: 11
		Offset: 0,-4
	damaged-active:
		Start: 11
		Length: 11
		Offset: 0,-4
	make:
		Filename: ironmake.shp
		Length: *
		Offset: 0,-4
	bib:
		TilesetFilenames:
			SNOW: mbIRON.sno
			INTERIOR: mbIRON.int
			TEMPERAT: mbIRON.tem
			DESERT: mbIRON.des
		Length: *
		Offset: 0,-2
	icon:
		Filename: ironicon.shp

pdox:
	Defaults:
		Filename: pdox.shp
	idle:
	damaged-idle:
		Start: 29
	active:
		Length: 29
	damaged-active:
		Start: 29
		Length: 29
	make:
		Filename: pdoxmake.shp
		Length: *
	bib:
		TilesetFilenames:
			SNOW: mbPDOX.sno
			INTERIOR: mbPDOX.int
			TEMPERAT: mbPDOX.tem
			DESERT: mbPDOX.des
		Length: *
		Offset: 0,-4
	icon:
		Filename: pdoxicon.shp
	fake-icon:
		Filename: pdoficon.shp

mslo:
	Defaults:
		Filename: mslo.tem
		TilesetFilenames:
			SNOW: mslo.sno
			DESERT: mslo.des
	idle:
	damaged-idle:
		Start: 8
	make:
		Filename: mslomake.tem
		TilesetFilenames:
			SNOW: mslomake.sno
			DESERT: mslomake.des
		Length: *
	active:
		Start: 1
		Length: 7
		Tick: 80
	damaged-active:
		Start: 9
		Length: 7
	icon:
		Filename: msloicon2.shp
		TilesetFilenames:
	fake-icon:
		Filename: mslficon.shp
		TilesetFilenames:

miss:
	Defaults:
		Filename: miss.shp
	idle:
	damaged-idle:
		Start: 1
	dead:
		Start: 2
		Tick: 800
	make:
		Filename: missmake.shp
		Length: *
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
		Length: *

brik:
	Defaults:
		Filename: brik.shp
	idle:
		Length: 16
	scratched-idle:
		Start: 16
		Length: 16
	damaged-idle:
		Start: 32
		Length: 16
	icon:
		Filename: brikicon.shp

sbag:
	idle:
		Filename: sbag.shp
		Length: 16
	icon:
		Filename: sbagicon.shp

fenc:
	idle:
		Filename: fenc.shp
		Length: 16
	icon:
		Filename: fencicon.shp

cycl:
	Defaults:
		Filename: cycl.shp
	idle:
		Length: 16
	damaged-idle:
		Start: 16
		Length: 16
