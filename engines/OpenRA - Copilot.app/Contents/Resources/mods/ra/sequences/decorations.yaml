tc04:
	Defaults:
		Filename: tc04.tem
		TilesetFilenames:
			SNOW: tc04.sno
	idle:

tc04.husk:
	Defaults:
		Filename: tc04.tem
		TilesetFilenames:
			SNOW: tc04.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

tc05:
	Defaults:
		Filename: tc05.tem
		TilesetFilenames:
			SNOW: tc05.sno
	idle:

tc05.husk:
	Defaults:
		Filename: tc05.tem
		TilesetFilenames:
			SNOW: tc05.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

tc03:
	Defaults:
		Filename: tc03.tem
		TilesetFilenames:
			SNOW: tc03.sno
	idle:

tc03.husk:
	Defaults:
		Filename: tc03.tem
		TilesetFilenames:
			SNOW: tc03.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

tc02:
	Defaults:
		Filename: tc02.tem
		TilesetFilenames:
			SNOW: tc02.sno
	idle:

tc02.husk:
	Defaults:
		Filename: tc02.tem
		TilesetFilenames:
			SNOW: tc02.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

tc01:
	Defaults:
		Filename: tc01.tem
		TilesetFilenames:
			SNOW: tc01.sno
			DESERT: tc01.des
	idle:

tc01.husk:
	Defaults:
		Filename: tc01.tem
		TilesetFilenames:
			SNOW: tc01.sno
			DESERT: tc01.des
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t17:
	Defaults:
		Filename: t17.tem
		TilesetFilenames:
			SNOW: t17.sno
	idle:

t17.husk:
	Defaults:
		Filename: t17.tem
		TilesetFilenames:
			SNOW: t17.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t16:
	Defaults:
		Filename: t16.tem
		TilesetFilenames:
			SNOW: t16.sno
	idle:

t16.husk:
	Defaults:
		Filename: t16.tem
		TilesetFilenames:
			SNOW: t16.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t15:
	Defaults:
		Filename: t15.tem
		TilesetFilenames:
			SNOW: t15.sno
	idle:

t15.husk:
	Defaults:
		Filename: t15.tem
		TilesetFilenames:
			SNOW: t15.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t14:
	Defaults:
		Filename: t14.tem
		TilesetFilenames:
			SNOW: t14.sno
	idle:

t14.husk:
	Defaults:
		Filename: t14.tem
		TilesetFilenames:
			SNOW: t14.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t13:
	Defaults:
		Filename: t13.tem
		TilesetFilenames:
			SNOW: t13.sno
	idle:

t13.husk:
	Defaults:
		Filename: t13.tem
		TilesetFilenames:
			SNOW: t13.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t12:
	Defaults:
		Filename: t12.tem
		TilesetFilenames:
			SNOW: t12.sno
	idle:

t12.husk:
	Defaults:
		Filename: t12.tem
		TilesetFilenames:
			SNOW: t12.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t11:
	Defaults:
		Filename: t11.tem
		TilesetFilenames:
			SNOW: t11.sno
	idle:

t11.husk:
	Defaults:
		Filename: t11.tem
		TilesetFilenames:
			SNOW: t11.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t10:
	Defaults:
		Filename: t10.tem
		TilesetFilenames:
			SNOW: t10.sno
	idle:

t10.husk:
	Defaults:
		Filename: t10.tem
		TilesetFilenames:
			SNOW: t10.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t08:
	Defaults:
		Filename: t08.tem
		TilesetFilenames:
			SNOW: t08.sno
			DESERT: t08.des
	idle:

t08.husk:
	Defaults:
		Filename: t08.tem
		TilesetFilenames:
			SNOW: t08.sno
			DESERT: t08.des
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t07:
	Defaults:
		Filename: t07.tem
		TilesetFilenames:
			SNOW: t07.sno
	idle:

t07.husk:
	Defaults:
		Filename: t07.tem
		TilesetFilenames:
			SNOW: t07.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t06:
	Defaults:
		Filename: t06.tem
		TilesetFilenames:
			SNOW: t06.sno
	idle:

t06.husk:
	Defaults:
		Filename: t06.tem
		TilesetFilenames:
			SNOW: t06.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t05:
	Defaults:
		Filename: t05.tem
		TilesetFilenames:
			SNOW: t05.sno
	idle:

t05.husk:
	Defaults:
		Filename: t05.tem
		TilesetFilenames:
			SNOW: t05.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t03:
	Defaults:
		Filename: t03.tem
		TilesetFilenames:
			SNOW: t03.sno
	idle:

t03.husk:
	Defaults:
		Filename: t03.tem
		TilesetFilenames:
			SNOW: t03.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t02:
	Defaults:
		Filename: t02.tem
		TilesetFilenames:
			SNOW: t02.sno
	idle:

t02.husk:
	Defaults:
		Filename: t02.tem
		TilesetFilenames:
			SNOW: t02.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t01:
	Defaults:
		Filename: t01.tem
		TilesetFilenames:
			SNOW: t01.sno
	idle:

t01.husk:
	Defaults:
		Filename: t01.tem
		TilesetFilenames:
			SNOW: t01.sno
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

ice01:
	idle:
		Filename: ice01.sno
		Length: *

ice02:
	idle:
		Filename: ice02.sno
		Length: *

ice03:
	idle:
		Filename: ice03.sno
		Length: *

ice04:
	idle:
		Filename: ice04.sno
		Length: *

ice05:
	idle:
		Filename: ice05.sno
		Length: *

v01:
	Defaults:
		Filename: v01.tem
		TilesetFilenames:
			SNOW: v01.sno
	idle:
	damaged-idle:
		Start: 1

v02:
	Defaults:
		Filename: v02.tem
		TilesetFilenames:
			SNOW: v02.sno
	idle:
	damaged-idle:
		Start: 1

v03:
	Defaults:
		Filename: v03.tem
		TilesetFilenames:
			SNOW: v03.sno
	idle:
	damaged-idle:
		Start: 1

v04:
	Defaults:
		Filename: v04.tem
		TilesetFilenames:
			SNOW: v04.sno
	idle:
	damaged-idle:
		Start: 2
	active:
		Start: 1
	damaged-active:
		Start: 3

v05:
	Defaults:
		Filename: v05.tem
		TilesetFilenames:
			SNOW: v05.sno
	idle:
	damaged-idle:
		Start: 2

v06:
	Defaults:
		Filename: v06.tem
		TilesetFilenames:
			SNOW: v06.sno
	idle:
	damaged-idle:
		Start: 1

v07:
	Defaults:
		Filename: v07.tem
		TilesetFilenames:
			SNOW: v07.sno
	idle:
	damaged-idle:
		Start: 2

v08:
	Defaults:
		Filename: v08.tem
		TilesetFilenames:
			SNOW: v08.sno
	idle:
	damaged-idle:
		Start: 1

v09:
	Defaults:
		Filename: v09.tem
		TilesetFilenames:
			SNOW: v09.sno
	idle:
	damaged-idle:
		Start: 1

v10:
	Defaults:
		Filename: v10.tem
		TilesetFilenames:
			SNOW: v10.sno
	idle:
	damaged-idle:
		Start: 1

v11:
	Defaults:
		Filename: v11.tem
		TilesetFilenames:
			SNOW: v11.sno
	idle:
	damaged-idle:
		Start: 1

v12:
	Defaults:
		Filename: v12.tem
		TilesetFilenames:
			SNOW: v12.sno
	idle:
		ZOffset: -2c512
	damaged-idle:
		Start: 1
		ZOffset: -2c512

v13:
	Defaults:
		Filename: v13.tem
		TilesetFilenames:
			SNOW: v13.sno
	idle:
	damaged-idle:
		Start: 1

v14:
	Defaults:
		Filename: v14.tem
		TilesetFilenames:
			SNOW: v14.sno
	idle:
		ZOffset: -2c512
	damaged-idle:
		Start: 1
		ZOffset: -2c512

v15:
	Defaults:
		Filename: v15.tem
		TilesetFilenames:
			SNOW: v15.sno
	idle:
		ZOffset: -2c512
	damaged-idle:
		Start: 1
		ZOffset: -2c512

v16:
	Defaults:
		Filename: v16.tem
		TilesetFilenames:
			SNOW: v16.sno
	idle:
		ZOffset: -2c512
	damaged-idle:
		Start: 1
		ZOffset: -2c512

v17:
	Defaults:
		Filename: v17.tem
		TilesetFilenames:
			SNOW: v17.sno
	idle:
		ZOffset: -2c512
	damaged-idle:
		Start: 1
		ZOffset: -2c512

v18:
	Defaults:
		Filename: v18.tem
		TilesetFilenames:
			SNOW: v18.sno
	idle:
		ZOffset: -2c512
	damaged-idle:
		Start: 1
		ZOffset: -2c512

rice:
	idle:
		Filename: rice.tem
		ZOffset: -2c512
	damaged-idle:
		Filename: rice.tem
		Start: 1
		ZOffset: -2c512

v19:
	idle:
		Filename: v19.shp
		Length: 14

v19.husk:
	idle:
		Filename: v19.shp
		Start: 28
	fire-start:
		Filename: flmspt.shp
		Length: *
		Offset: 7,-15
		ZOffset: 1
	fire-loop:
		Filename: flmspt.shp
		Start: 50
		Length: *
		Offset: 7,-15
		ZOffset: 1

utilpol1:
	Defaults:
		Filename: utilpol1.shp
	idle:
	damaged-idle:
		Start: 1
	dead:
		Start: 1

utilpol2:
	Defaults:
		Filename: utilpol2.shp
	idle:
	damaged-idle:
		Start: 1
	dead:
		Start: 1

ammobox1:
	idle:
		Filename: ammobox1.shp

ammobox2:
	idle:
		Filename: ammobox2.shp

ammobox3:
	idle:
		Filename: ammobox3.shp

tanktrap1:
	idle:
		Filename: tanktrap1.shp

tanktrap2:
	idle:
		Filename: tanktrap2.shp

rushouse:
	Defaults:
		Filename: rushouse.shp
	idle:
	damaged-idle:
		Start: 1

asianhut:
	Defaults:
		Filename: asianhut.shp
	idle:
	damaged-idle:
		Start: 1

barb:
	Defaults:
		Filename: barb.shp
	idle:
		Length: 16
	damaged-idle:
		Start: 16
		Length: 16

wood:
	Defaults:
		Filename: wood.shp
	idle:
		Length: 16
	damaged-idle:
		Start: 16
		Length: 16

barl:
	idle:
		Filename: barl.shp

brl3:
	idle:
		Filename: brl3.shp

# Interior Terrain
boxes01:
	idle:
		Filename: boxes01.int

boxes02:
	idle:
		Filename: boxes02.int

boxes03:
	idle:
		Filename: boxes03.int

boxes04:
	idle:
		Filename: boxes04.int

boxes05:
	idle:
		Filename: boxes05.int

boxes06:
	idle:
		Filename: boxes06.int

boxes07:
	idle:
		Filename: boxes07.int

boxes08:
	idle:
		Filename: boxes08.int

boxes09:
	idle:
		Filename: boxes09.int

# Desert Terrain Expansion
rock1:
	idle:
		Filename: rock1.des

rock2:
	idle:
		Filename: rock2.des

rock3:
	idle:
		Filename: rock3.des

rock4:
	idle:
		Filename: rock4.des

rock5:
	idle:
		Filename: rock5.des

rock6:
	idle:
		Filename: rock6.des

rock7:
	idle:
		Filename: rock7.des

t04:
	idle:
		Filename: t04.des

t04.husk:
	Defaults:
		Filename: t04.des
	idle:
		Start: 1
	dead:
		Filename: t04.des
		Start: 2
		Length: 8
		Tick: 80

t09:
	idle:
		Filename: t09.des

t09.husk:
	idle:
		Filename: t09.des
		Start: 1
	dead:
		Filename: t09.des
		Start: 2
		Length: 8
		Tick: 80

v20:
	idle:
		Filename: v20.des
		Length: 3
		Tick: 120
	damaged-idle:
		Filename: v20.des
		Start: 3
		Length: 3
		Tick: 120

v21:
	idle:
		Filename: v21.des
		Length: 3
		Tick: 120
	damaged-idle:
		Filename: v21.des
		Start: 3
		Length: 3
		Tick: 120

v22:
	idle:
		Filename: v22.des
		Length: 3
		Tick: 120
	damaged-idle:
		Filename: v22.des
		Start: 3
		Length: 3
		Tick: 120

v23:
	idle:
		Filename: v23.des
		Length: 3
		Tick: 120
	damaged-idle:
		Filename: v23.des
		Start: 3
		Length: 3
		Tick: 120

v24:
	idle:
		Filename: v24.des
	damaged-idle:
		Filename: v24.des
		Start: 1

v25:
	idle:
		Filename: v25.des
	damaged-idle:
		Filename: v25.des
		Start: 1

v26:
	idle:
		Filename: v26.des
	damaged-idle:
		Filename: v26.des
		Start: 1

v27:
	idle:
		Filename: v27.des
	damaged-idle:
		Filename: v27.des
		Start: 1

v28:
	idle:
		Filename: v28.des
	damaged-idle:
		Filename: v28.des
		Start: 1

v29:
	idle:
		Filename: v29.des
	damaged-idle:
		Filename: v29.des
		Start: 1

v30:
	idle:
		Filename: v30.des
	damaged-idle:
		Filename: v30.des
		Start: 2

v31:
	idle:
		Filename: v31.des
	damaged-idle:
		Filename: v31.des
		Start: 1

v32:
	idle:
		Filename: v32.des
	damaged-idle:
		Filename: v32.des
		Start: 1

v33:
	idle:
		Filename: v33.des
	damaged-idle:
		Filename: v33.des
		Start: 1

v34:
	idle:
		Filename: v34.des
	damaged-idle:
		Filename: v34.des
		Start: 1

v35:
	idle:
		Filename: v35.des
	damaged-idle:
		Filename: v35.des
		Start: 1

v36:
	idle:
		Filename: v36.des
	damaged-idle:
		Filename: v36.des
		Start: 1

v37:
	idle:
		Filename: v37.des
	damaged-idle:
		Filename: v37.des
		Start: 1

snowhut:
	Defaults:
		Filename: snowhut.shp
		Offset: 0,-5
		Scale: 0.7
	idle:
		Length: 3
		Tick: 360
	damaged-idle:
		Start: 3
		Tick: 120

lhus:
	Defaults:
		Filename: lhus.shp
		Offset: 0,-16
	idle:
		Length: 16
		Tick: 180
	damaged-idle:
		Start: 16
		Tick: 180
		Length: 8

windmill:
	Defaults:
		Filename: windmill.shp
		Offset: 0,-16
	idle:
		Length: 8
		Tick: 80
	damaged-idle:
		Start: 8
		Length: 8
		Tick: 80
