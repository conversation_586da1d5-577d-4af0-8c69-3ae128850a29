mcv:
	idle:
		Filename: mcv.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: mcvicon.shp

mcvhusk:
	idle:
		Filename: mcvhusk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -1023

truk:
	idle:
		Filename: truk.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: trukicon.shp

harv:
	Defaults:
		Filename: harv.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	harvest:
		Start: 32
		Length: 8
		Facings: 8
	dock:
		Filename: harv.shp
		Start: 96
		Length: 8
	dock-loop:
		Filename: harv.shp
		Start: 104
		Length: 7
	icon:
		Filename: harvicon.shp
		Start: 0

harvempty:
	Inherits: harv
	Defaults:
		Filename: harvempty.shp

harvhalf:
	Inherits: harv
	Defaults:
		Filename: harvhalf.shp

hhusk:
	idle:
		Filename: hhusk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -1023

hhusk2:
	idle:
		Filename: hhusk2.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -1023

1tnk:
	Defaults:
		Filename: 1tnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 2
	icon:
		Filename: 1tnkicon.shp

1tnk.destroyed:
	idle:
		Filename: 1tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 1tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

2tnk:
	Defaults:
		Filename: 2tnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 2tnkicon.shp

2tnk.destroyed:
	idle:
		Filename: 2tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 2tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

3tnk:
	Defaults:
		Filename: 3tnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 3tnkicon.shp

3tnk.destroyed:
	idle:
		Filename: 3tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 3tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

4tnk:
	Defaults:
		Filename: 4tnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 4tnkicon.shp

4tnk.destroyed:
	idle:
		Filename: 4tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 4tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

v2rl:
	Defaults:
		Filename: v2rl.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	empty-idle:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: v2rlicon.shp

arty:
	idle:
		Filename: arty.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: artyicon.shp

jeep:
	Defaults:
		Filename: jeep.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	icon:
		Filename: jeepicon.shp

apc:
	Defaults:
		Filename: apc.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	open:
		Start: 32
		Length: 3
	unload:
		Start: 32
	icon:
		Filename: apcicon.shp

mnly:
	idle:
		Filename: mnly.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: mnlyicon.shp

mrj:
	Defaults:
		Filename: mrj.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	spinner:
		Start: 32
		Length: 32
	icon:
		Filename: mrjicon.shp

mgg:
	Defaults:
		Filename: mgg.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	spinner:
		Start: 32
		Length: 8
	spinner-idle:
		Start: 32
		Length: 1
	icon:
		Filename: mggicon.shp

mgg.destroyed:
	idle:
		Filename: mgg.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	spinner:
		Filename: mgg.shp
		Start: 32
		Length: 8
		ZOffset: -512
	spinner-idle:
		Filename: mgg.shp
		Start: 32

ttnk:
	Defaults:
		Filename: ttnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	spinner:
		Start: 32
		Length: 32
	icon:
		Filename: ttnkicon.shp

ftrk:
	Defaults:
		Filename: ftrk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 2
	icon:
		Filename: ftrkicon.shp

dtrk:
	idle:
		Filename: dtrk.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: dtrkicon.shp

ctnk:
	idle:
		Filename: ctnk.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: ctnkicon.shp

qtnk:
	Defaults:
		Filename: qtnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	piston:
		Start: 32
		Facings: 8
		Length: 8
	icon:
		Filename: qtnkicon.shp

stnk:
	Defaults:
		Filename: stnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 38
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: stnkicon.shp
