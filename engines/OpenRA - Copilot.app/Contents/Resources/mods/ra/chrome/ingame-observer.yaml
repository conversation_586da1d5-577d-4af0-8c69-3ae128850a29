Container@OBSERVER_WIDGETS:
	Logic: LoadIngameChatLogic
	Children:
		Container@CHAT_ROOT:
		Container@MUTE_INDICATOR:
			Logic: MuteIndicatorLogic
			X: WINDOW_RIGHT - WIDTH - 260
			Y: 10
			Width: 200
			Height: 25
			Children:
				Image@ICON:
					X: PARENT_RIGHT - WIDTH
					Y: 1
					Width: 24
					Height: 24
					ImageCollection: sidebar-bits
					ImageName: indicator-muted
				Label@LABEL:
					Width: PARENT_RIGHT - 30
					Height: 25
					Align: Right
					Text: label-mute-indicator
					Contrast: true
		Image@SIDEBAR_BACKGROUND_TOP:
			X: WINDOW_RIGHT - 250
			Y: 10
			Width: 238
			Height: 287
			ImageCollection: sidebar-observer
			ImageName: background
			ClickThrough: false
			Children:
				Background@GAME_TIMER_BLOCK:
					Logic: GameTimerLogic
					X: 26
					Y: 10
					Width: 120
					Height: 22
					Background: sidebar-button-observer
					Children:
						LabelWithTooltip@GAME_TIMER:
							Y: 0 - 1
							Width: PARENT_RIGHT
							Height: PARENT_BOTTOM
							Align: Center
							Font: TinyBold
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
				Container@TOP_BUTTONS:
					Logic: MenuButtonsChromeLogic
					X: 9
					Y: 7
					Children:
						LogicKeyListener@OBSERVER_KEY_LISTENER:
						MenuButton@OPTIONS_BUTTON:
							Key: escape
							X: 192
							Width: 28
							Height: 28
							Background: sidebar-button-observer
							TooltipText: button-top-buttons-options-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							DisableWorldSounds: true
							VisualHeight: 0
							Children:
								Image@ICON:
									X: 6
									Y: 6
									ImageCollection: order-icons
									ImageName: options
				Container@RADAR:
					Children:
						ColorBlock:
							X: 8
							Y: 40
							Width: 222
							Height: 222
							Color: 000000
						Radar@INGAME_RADAR:
							WorldInteractionController: INTERACTION_CONTROLLER
							X: 9
							Y: 41
							Width: 220
							Height: 220
						VideoPlayer@PLAYER:
							X: 9
							Y: 41
							Width: 220
							Height: 220
							Skippable: false
				DropDownButton@SHROUD_SELECTOR:
					Logic: ObserverShroudSelectorLogic
						CombinedViewKey: ObserverCombinedView
						WorldViewKey: ObserverWorldView
					X: 6
					Y: 262
					Width: 226
					Height: 25
					Font: Bold
					VisualHeight: 0
					Background: sidebar-button-observershroud
					SeparatorImage: observer-separator
					Children:
						LogicKeyListener@SHROUD_KEYHANDLER:
						Image@FLAG:
							X: 4
							Y: 6
							Width: 32
							Height: 16
						Label@LABEL:
							X: 40
							Width: PARENT_RIGHT
							Height: 25
							Shadow: True
						Label@NOFLAG_LABEL:
							X: 5
							Width: PARENT_RIGHT
							Height: 25
							Shadow: True
		Image@SIDEBAR_BACKGROUND_BOTTOM:
			X: WINDOW_RIGHT - 250
			Y: 297
			Width: 238
			Height: 8
			ImageCollection: sidebar-observer
			ImageName: observer-bottom
		Image@REPLAY_PLAYER:
			Logic: ReplayControlBarLogic
			X: WINDOW_RIGHT - 250
			Y: 297
			Width: 238
			Height: 40
			Visible: false
			ImageCollection: sidebar-observer
			ImageName: replay-bottom
			ClickThrough: false
			Visible: false
			Children:
				Button@BUTTON_PAUSE:
					X: 9
					Y: 5
					Width: 28
					Height: 28
					Background: sidebar-button-observer
					Key: Pause
					TooltipText: button-observer-widgets-pause-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Children:
						Image@IMAGE_PAUSE:
							X: 6
							Y: 6
							ImageCollection: music
							ImageName: pause
				Button@BUTTON_PLAY:
					X: 9
					Y: 5
					Width: 28
					Height: 28
					Background: sidebar-button-observer
					Key: Pause
					TooltipText: button-observer-widgets-play-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Children:
						Image@IMAGE_PLAY:
							X: 6
							Y: 6
							ImageCollection: music
							ImageName: play
				Button@BUTTON_SLOW:
					X: 49
					Y: 8
					Width: 42
					Height: 22
					Background: sidebar-button-observer
					Key: ReplaySpeedSlow
					TooltipText: button-observer-widgets-slow.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Text: button-observer-widgets-slow.label
					Font: TinyBold
				Button@BUTTON_REGULAR:
					X: 95
					Y: 8
					Width: 42
					Height: 22
					Background: sidebar-button-observer
					Key: ReplaySpeedRegular
					TooltipText: button-observer-widgets-regular.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Text: button-observer-widgets-regular.label
					Font: TinyBold
				Button@BUTTON_FAST:
					X: 141
					Y: 8
					Width: 42
					Height: 22
					Background: sidebar-button-observer
					Key: ReplaySpeedFast
					TooltipText: button-observer-widgets-fast.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Text: button-observer-widgets-fast.label
					Font: TinyBold
				Button@BUTTON_MAXIMUM:
					X: 187
					Y: 8
					Width: 42
					Height: 22
					Background: sidebar-button-observer
					Key: ReplaySpeedMax
					TooltipText: button-observer-widgets-maximum.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Text: button-observer-widgets-maximum.label
					Font: TinyBold
		Container@INGAME_OBSERVERSTATS_BG:
			Logic: ObserverStatsLogic
				StatisticsNoneKey: StatisticsNone
				StatisticsBasicKey: StatisticsBasic
				StatisticsEconomyKey: StatisticsEconomy
				StatisticsProductionKey: StatisticsProduction
				StatisticsSupportPowersKey: StatisticsSupportPowers
				StatisticsCombatKey: StatisticsCombat
				StatisticsArmyKey: StatisticsArmy
				StatisticsGraphKey: StatisticsGraph
				StatisticsArmyGraphKey: StatisticsArmyGraph
				StatsDropDownPanelTemplate: SPECTATOR_LABEL_DROPDOWN_TEMPLATE
			X: 5
			Y: 5
			Width: 760
			Height: 240
			Children:
				DropDownButton@STATS_DROPDOWN:
					X: 0
					Y: 0
					Width: 185
					Height: 25
					Font: Bold
					Background: observer-scrollpanel-button
					SeparatorImage: observer-separator
					Children:
						LogicKeyListener@STATS_DROPDOWN_KEYHANDLER:
				Container@GRAPH_BG:
					Y: 30
					X: 0
					Width: PARENT_RIGHT
					Height: 24
					Children:
						Container@BASIC_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 705
							Height: PARENT_BOTTOM
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_BOTTOM
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-player-header
									Align: Left
									Shadow: True
								Label@CASH_HEADER:
									X: 160
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-cash-header
									Align: Right
									Shadow: True
								Label@POWER_HEADER:
									X: 240
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-power-header
									Align: Center
									Shadow: True
								Label@KILLS_HEADER:
									X: 320
									Y: 0
									Width: 40
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-kills-header
									Align: Right
									Shadow: True
								Label@DEATHS_HEADER:
									X: 360
									Y: 0
									Width: 60
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-deaths-header
									Align: Right
									Shadow: True
								Label@ASSETS_DESTROYED_HEADER:
									X: 420
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-assets-destroyed-header
									Align: Right
									Shadow: True
								Label@ASSETS_LOST_HEADER:
									X: 500
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-assets-lost-header
									Align: Right
									Shadow: True
								Label@EXPERIENCE_HEADER:
									X: 580
									Y: 0
									Width: 60
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-experience-header
									Align: Right
									Shadow: True
								Label@ACTIONS_MIN_HEADER:
									X: 640
									Y: 0
									Width: 60
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-basic-stats-actions-min-header
									Align: Right
									Shadow: True
						Container@ECONOMY_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 735
							Height: PARENT_BOTTOM
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_BOTTOM
								Label@PLAYER_HEADER:
									X: 40
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-economy-stats-player-header
									Shadow: True
								Label@CASH_HEADER:
									X: 160
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-economy-stats-cash-header
									Align: Right
									Shadow: True
								Label@INCOME_HEADER:
									X: 240
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-economy-stats-income-header
									Align: Right
									Shadow: True
								Label@ASSETS_HEADER:
									X: 320
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-economy-stats-assets-header
									Align: Right
									Shadow: True
								Label@EARNED_HEADER:
									X: 400
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-economy-stats-earned-header
									Align: Right
									Shadow: True
								Label@SPENT_HEADER:
									X: 480
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-economy-stats-spent-header
									Align: Right
									Shadow: True
								Label@HARVESTERS_HEADER:
									X: 560
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-economy-stats-harvesters-header
									Align: Right
									Shadow: True
								Label@DERRICKS_HEADER:
									X: 650
									Width: 80
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-economy-stats-derricks-header
									Align: Right
									Shadow: True
						Container@PRODUCTION_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_BOTTOM
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_BOTTOM
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-production-stats-player-header
									Align: Left
									Shadow: True
								Label@PRODUCTION_HEADER:
									X: 160
									Y: 0
									Width: 100
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-production-stats-header
									Shadow: True
						Container@SUPPORT_POWERS_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_BOTTOM
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_BOTTOM
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-support-powers-player-header
									Align: Left
									Shadow: True
								Label@SUPPORT_POWERS_HEADER:
									X: 160
									Y: 0
									Width: 200
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-support-powers-header
									Shadow: True
						Container@ARMY_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_BOTTOM
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_BOTTOM
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-army-player-header
									Align: Left
									Shadow: True
								Label@ARMY_HEADER:
									X: 160
									Y: 0
									Width: 100
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-army-header
									Shadow: True
						Container@COMBAT_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 760
							Height: PARENT_BOTTOM
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_BOTTOM
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-player-header
									Align: Left
									Shadow: True
								Label@ASSETS_DESTROYED_HEADER:
									X: 160
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-assets-destroyed-header
									Align: Right
									Shadow: True
								Label@ASSETS_LOST_HEADER:
									X: 235
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-assets-lost-header
									Align: Right
									Shadow: True
								Label@UNITS_KILLED_HEADER:
									X: 310
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-units-killed-header
									Align: Right
									Shadow: True
								Label@UNITS_DEAD_HEADER:
									X: 385
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-units-dead-header
									Align: Right
									Shadow: True
								Label@BUILDINGS_KILLED_HEADER:
									X: 460
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-buildings-killed-header
									Align: Right
									Shadow: True
								Label@BUILDINGS_DEAD_HEADER:
									X: 535
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-buildings-dead-header
									Align: Right
									Shadow: True
								Label@ARMY_VALUE_HEADER:
									X: 610
									Y: 0
									Width: 90
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-army-value-header
									Align: Right
									Shadow: True
								Label@VISION_HEADER:
									X: 700
									Y: 0
									Width: 60
									Height: PARENT_BOTTOM
									Font: Bold
									Text: label-combat-stats-vision-header
									Align: Right
									Shadow: True
				ScrollPanel@PLAYER_STATS_PANEL:
					X: 0
					Y: 54
					Width: PARENT_RIGHT
					Height: 240
					TopBottomSpacing: 0
					BorderWidth: 0
					Background:
					ScrollBarBackground: observer-scrollpanel-button
					Button: observer-scrollpanel-button
					ScrollbarWidth: 24
					ScrollBar: Hidden
					Children:
						ScrollItem@TEAM_TEMPLATE:
							X: 0
							Y: 0
							Width: 650
							Height: 24
							Children:
								ColorBlock@TEAM_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@TEAM_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_BOTTOM
								Label@TEAM:
									X: 10
									Y: 0
									Width: PARENT_RIGHT
									Height: PARENT_BOTTOM
									Font: Bold
									Shadow: True
						ScrollItem@BASIC_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 705
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									Width: 200
									Height: PARENT_BOTTOM
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_BOTTOM - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Shadow: True
								Label@CASH:
									X: 160
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@POWER:
									X: 240
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Center
									Shadow: True
								Label@KILLS:
									X: 320
									Y: 0
									Width: 40
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@DEATHS:
									X: 360
									Y: 0
									Width: 60
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@ASSETS_DESTROYED:
									X: 420
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@ASSETS_LOST:
									X: 500
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@EXPERIENCE:
									X: 580
									Y: 0
									Width: 60
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@ACTIONS_MIN:
									X: 640
									Y: 0
									Width: 60
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
						ScrollItem@ECONOMY_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 735
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									Width: 200
									Height: PARENT_BOTTOM
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_BOTTOM - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Shadow: True
								Label@CASH:
									X: 160
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@INCOME:
									X: 240
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@ASSETS:
									X: 320
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@EARNED:
									X: 400
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@SPENT:
									X: 480
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@HARVESTERS:
									X: 560
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@DERRICKS:
									X: 650
									Y: 0
									Width: 80
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
						ScrollItem@PRODUCTION_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									Width: 200
									Height: PARENT_BOTTOM
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_BOTTOM - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Shadow: True
								ObserverProductionIcons@PRODUCTION_ICONS:
									X: 160
									Y: 0
									Width: 0
									Height: PARENT_BOTTOM
									TooltipContainer: TOOLTIP_CONTAINER
						ScrollItem@SUPPORT_POWERS_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									Width: 200
									Height: PARENT_BOTTOM
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_BOTTOM - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Shadow: True
								ObserverSupportPowerIcons@SUPPORT_POWER_ICONS:
									X: 160
									Y: 0
									Width: 0
									Height: PARENT_BOTTOM
									TooltipContainer: TOOLTIP_CONTAINER
						ScrollItem@ARMY_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									Width: 200
									Height: PARENT_BOTTOM
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_BOTTOM - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Shadow: True
								ObserverArmyIcons@ARMY_ICONS:
									X: 160
									Y: 0
									Width: 0
									Height: PARENT_BOTTOM
									TooltipContainer: TOOLTIP_CONTAINER
						ScrollItem@COMBAT_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 760
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_RIGHT - 200
									Height: PARENT_BOTTOM
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_RIGHT - 200
									Y: 0
									Width: 200
									Height: PARENT_BOTTOM
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_BOTTOM - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_BOTTOM
									Font: Bold
									Shadow: True
								Label@ASSETS_DESTROYED:
									X: 160
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@ASSETS_LOST:
									X: 235
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@UNITS_KILLED:
									X: 310
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@UNITS_DEAD:
									X: 385
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@BUILDINGS_KILLED:
									X: 460
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@BUILDINGS_DEAD:
									X: 535
									Y: 0
									Width: 75
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@ARMY_VALUE:
									X: 610
									Y: 0
									Width: 90
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
								Label@VISION:
									X: 700
									Y: 0
									Width: 60
									Height: PARENT_BOTTOM
									Align: Right
									Shadow: True
				Container@INCOME_GRAPH_CONTAINER:
					X: 0
					Y: 30
					Width: PARENT_RIGHT
					Height: PARENT_BOTTOM
					Visible: False
					Children:
						ColorBlock@GRAPH_BACKGROUND:
							X: 0
							Y: 0
							Width: PARENT_RIGHT
							Height: PARENT_BOTTOM
							Color: 00000090
						LineGraph@INCOME_GRAPH:
							X: 0
							Y: 0
							Width: PARENT_RIGHT - 5
							Height: PARENT_BOTTOM
							ValueFormat: ${0}
							YAxisValueFormat: ${0:F0}
							XAxisSize: 40
							XAxisTicksPerLabel: 2
							XAxisLabel: Game Minute
							YAxisLabel: Earnings
							LabelFont: TinyBold
							AxisFont: TinyBold
				Container@ARMY_VALUE_GRAPH_CONTAINER:
					X: 0
					Y: 30
					Width: PARENT_RIGHT
					Height: PARENT_BOTTOM
					Visible: False
					Children:
						ColorBlock@GRAPH_BACKGROUND:
							X: 0
							Y: 0
							Width: PARENT_RIGHT
							Height: PARENT_BOTTOM
							Color: 00000090
						LineGraph@ARMY_VALUE_GRAPH:
							X: 0
							Y: 0
							Width: PARENT_RIGHT - 5
							Height: PARENT_BOTTOM
							ValueFormat: ${0}
							YAxisValueFormat: ${0:F0}
							XAxisSize: 40
							XAxisTicksPerLabel: 2
							XAxisLabel: Game Minute
							YAxisLabel: Army Value
							LabelFont: TinyBold
							AxisFont: TinyBold
		Container@HPF_ROOT:
			Logic: LoadIngameHierarchicalPathFinderOverlayLogic
			X: WINDOW_RIGHT - WIDTH - 255
			Y: 40
			Width: 175

