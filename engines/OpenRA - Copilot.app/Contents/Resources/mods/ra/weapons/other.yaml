^FireWeapon:
	ValidTargets: Ground, Water, Ground<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trees
	ReloadDelay: 70
	Range: 5c0
	Warhead@1Dam: SpreadDamage
		Spread: 213
		Damage: 15000
		ValidTargets: GroundActor, WaterActor, Trees
		Versus:
			None: 90
			Wood: 50
			Tree: 50
			Light: 60
			Heavy: 25
			Concrete: 20
		DamageTypes: Prone50Percent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eat<PERSON>, Incendiary
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Structure, Wall
	Warhead@3Eff: CreateEffect
		Explosions: napalm
		ImpactSounds: firebl3.aud
		ImpactActors: false

FireballLauncher:
	Inherits: ^FireWeapon
	Burst: 2
	BurstDelays: 20
	Projectile: Bullet
		Speed: 250
		TrailImage: fb2
		Image: FB1
	Warhead@1Dam: SpreadDamage
		Versus:
			Light: 50

Flamer:
	Inherits: ^FireWeapon
	ReloadDelay: 50
	Burst: 15
	BurstDelays: 1
	Projectile: Bullet
		Speed: 170
		TrailImage: fb4
		Image: fb3
		LaunchAngle: 62
		Inaccuracy: 853
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 1000
		Versus:
			None: 70
			Wood: 80
			Tree: 80
			Light: 40
			Heavy: 20
			Concrete: 10
	Warhead@2Smu: LeaveSmudge
		Chance: 15
	Warhead@3Eff: CreateEffect
		Explosions: small_napalm

Napalm:
	Inherits: ^FireWeapon
	ReloadDelay: 20
	Range: 4c512
	Projectile: Bullet
		Image: BOMBLET
		Speed: 85
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 170
		Damage: 10000
		Versus:
			Wood: 100
			Concrete: 50

^TeslaWeapon:
	ReloadDelay: 3
	Range: 7c0
	Report: tesla1.aud
	Projectile: TeslaZap
	ValidTargets: Ground, Water, GroundActor, WaterActor
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 10000
		ValidTargets: GroundActor, WaterActor
		Versus:
			None: 1000
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath

TeslaZap:
	Inherits: ^TeslaWeapon
	Warhead@1Dam: SpreadDamage
		Versus:
			Wood: 60

PortaTesla:
	Inherits: ^TeslaWeapon
	ReloadDelay: 70
	Range: 6c0
	Warhead@1Dam: SpreadDamage
		Damage: 4500
		Versus:
			Wood: 73
			Heavy: 60

TTankZap:
	Inherits: ^TeslaWeapon
	ReloadDelay: 120
	Range: 7c0

DogJaw:
	ValidTargets: Infantry
	ReloadDelay: 10
	Range: 3c0
	Report: dogg5p.aud
	TargetActorCenter: true
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		Damage: 100000
		ValidTargets: Infantry
		InvalidTargets: Ant
		DamageTypes: DefaultDeath

Heal:
	ReloadDelay: 80
	Range: 4c0
	Report: heal2.aud
	ValidTargets: Heal
	Projectile: Bullet
		Speed: 1c682
	Warhead@1Eff: FlashTargetsInRadius
		Radius: 1065
		ValidRelationships: Ally
		ValidTargets: Heal
	Warhead@2Dam: SpreadDamage
		Spread: 213
		Damage: -5000
		ValidRelationships: Ally
		ValidTargets: Heal
		DebugOverlayColor: 00FF00

Repair:
	Inherits: Heal
	Report: fixit1.aud
	ValidTargets: Repair
	Warhead@1Eff: FlashTargetsInRadius
		ValidTargets: Repair
	Warhead@2Dam: SpreadDamage
		Damage: -2000
		ValidTargets: Repair

Demolish:
	ValidTargets: GroundActor, WaterActor
	Warhead@1Dam: SpreadDamage
		DamageTypes: DefaultDeath
		ValidTargets: GroundActor, WaterActor
	Warhead@2Eff: CreateEffect
		Explosions: building
		ImpactSounds: kaboom25.aud

Claw:
	ReloadDelay: 30
	Range: 1c512
	ValidTargets: Ground, Water, GroundActor, WaterActor
	Projectile: Bullet
		Speed: 1c682
	Warhead@1Dam: SpreadDamage
		Spread: 213
		Damage: 3000
		ValidTargets: GroundActor, WaterActor
		Versus:
			None: 97
			Wood: 10
			Light: 30
			Heavy: 10
			Concrete: 10
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath

Mandible:
	Inherits: Claw
	ReloadDelay: 10
	Burst: 2
	BurstDelays: 14
	StartBurstReport: antbite.aud
	Warhead@1Dam: SpreadDamage
		Damage: 6000
		Versus:
			None: 90
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath

MandibleHeavy:
	Inherits: Claw
	ReloadDelay: 15
	Burst: 2
	BurstDelays: 20
	StartBurstReport: antbite.aud
	Warhead@1Dam: SpreadDamage
		Damage: 10000
		Versus:
			None: 100
			Light: 90
			Heavy: 35
			Concrete: 20
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath

AntFireball:
	Inherits: FireballLauncher
	Report: antbite.aud
	ReloadDelay: 50
	Burst: 2
	BurstDelays: 20
	Range: 4c0
	Projectile: Bullet
		Speed: 200
	Warhead@1Dam: SpreadDamage
		Spread: 213
		Damage: 4000
		Versus:
			None: 80
			Wood: 85
			Light: 45
			Heavy: 20
			Concrete: 15

DemoTruckTargeting:
	ValidTargets: DetonateAttack
	Range: 2c0
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		ValidTargets: DetonateAttack

MADTankThump:
	ValidTargets: GroundActor, WaterActor
	InvalidTargets: MADTank, Infantry
	Warhead@1Dam: HealthPercentageDamage
		Spread: 7c0
		Damage: 1
		ValidTargets: GroundActor, WaterActor
		InvalidTargets: MADTank, Infantry
	Warhead@Shake: ShakeScreen
		Duration: 10
		Intensity: 3
		Multiplier: 1,0

MADTankDetonate:
	ValidTargets: GroundActor, WaterActor
	InvalidTargets: MADTank, Infantry
	Warhead@1Dam: HealthPercentageDamage
		Spread: 7c0
		Damage: 19
		ValidTargets: GroundActor, WaterActor
		InvalidTargets: MADTank, Infantry
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		Size: 7,6
	Warhead@3Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: mineblo1.aud
		ImpactActors: false
