^Explosion:
	ValidTargets: Ground, Water, Air, GroundActor, <PERSON><PERSON>ctor, AirborneActor
	Warhead@1Dam: SpreadDamage
		Spread: 426
		Damage: 5000
		ValidTargets: GroundActor, WaterActor
		Versus:
			None: 90
			Wood: 75
			Light: 60
			Heavy: 25
			Concrete: 100
		DamageTypes: <PERSON>ne50<PERSON><PERSON>cent, <PERSON><PERSON><PERSON><PERSON>, Explosion<PERSON><PERSON><PERSON>
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		ValidTargets: Ground, Vehicle, Infantry
	Warhead@2Eff: CreateEffect
		Explosions: self_destruct
		ImpactSounds: kaboom22.aud
		ValidTargets: Ground, Air, GroundActor, AirborneActor, WaterActor, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: large_splash
		ImpactSounds: splash9.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Bridge

CrateNapalm:
	Inherits: ^Explosion
	ValidTargets: Ground, GroundActor, WaterActor, Trees
	Warhead@1Dam: SpreadDamage
		Spread: 170
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: GroundActor, WaterActor, Trees
		Versus:
			Wood: 100
			Concrete: 50
		AffectsParent: true
		DamageTypes: <PERSON>ne50P<PERSON>cent, <PERSON><PERSON><PERSON>rone, FireDeath, Incendiary
	Warhead@2Eff: CreateEffect
		Explosions: napalm
		ImpactSounds: firebl3.aud
		ValidTargets: Ground, Water, Air, GroundActor, AirborneActor, WaterActor, Trees
	-Warhead@3EffWater:
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch

CrateExplosion:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		AffectsParent: true
	Warhead@2Eff: CreateEffect
		ValidTargets: Ground, Water, Air, GroundActor, AirborneActor, WaterActor, Trees
	-Warhead@3EffWater:

UnitExplode:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Falloff: 1000, 368, 135, 50, 18, 7, 0

UnitExplodePlane:
	Inherits: UnitExplode
	Warhead@2Eff: CreateEffect
		Explosions: large_napalm

UnitExplodeHeli:
	Inherits: UnitExplode
	Warhead@2Eff: CreateEffect
		Explosions: napalm

VisualExplode:
	Inherits: ^Explosion
	-Warhead@1Dam:
	Warhead@2Eff: CreateEffect
		Explosions: offset_napalm
		ImpactSounds: firebl3.aud

UnitExplodeShip:
	Inherits: ^Explosion
	-Warhead@Smu:
	Warhead@2Eff: CreateEffect
		Explosions: building
		ImpactSounds: kaboom25.aud
		ValidTargets: Ground, Water
		ImpactActors: false
	Warhead@3EffWater: CreateEffect
		ValidTargets: Water
		ImpactActors: false

UnitExplodeSubmarine:
	Inherits: ^Explosion
	-Warhead@Smu:
	Warhead@2Eff: CreateEffect
		Explosions: large_splash
		ImpactSounds: splash9.aud
		ValidTargets: Water
		ImpactActors: false
	-Warhead@3EffWater:

UnitExplodeSmall:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Damage: 4000
	Warhead@2Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: kaboom15.aud

ArtilleryExplode:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Damage: 15000
	Warhead@2Eff: CreateEffect
		Explosions: self_destruct
		ImpactSounds: kaboom22.aud

V2Explode:
	Inherits: SCUD
	-Report:

BuildingExplode:
	ValidTargets: Ground, Water, GroundActor, WaterActor
	Warhead@2Eff: CreateEffect
		Explosions: building, building_napalm, large_explosion, self_destruct, large_napalm
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		ValidTargets: GroundActor
		InvalidTargets: Wall

SmallBuildingExplode:
	Inherits: BuildingExplode
	Warhead@2Eff: CreateEffect
		Explosions: building, building_napalm, large_explosion, self_destruct

CivPanicExplosion:
	ValidTargets: Ground, GroundActor
	Warhead@1Dam: SpreadDamage # Used to panic civilians which are emitted from a killed CivBuilding
		ValidTargets: Infantry
		Falloff: 100, 100
		Range: 0, 128
		Damage: 1
		Delay: 1

BarrelExplode:
	Warhead@Cluster: FireCluster
		Weapon: BarrelCluster
		Dimensions: 3,3
		Footprint: _X_ X_X _X_
	Warhead@1Dam: SpreadDamage
		Spread: 325
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: GroundActor, Trees
		Versus:
			None: 120
			Light: 50
			Wood: 20
			Concrete: 10
	Warhead@2Eff: CreateEffect
		Explosions: building
		ImpactSounds: kaboom25.aud
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater

BarrelCluster:
	Inherits: ^Explosion
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 325
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: GroundActor, Trees
		Versus:
			None: 120
			Light: 50
			Wood: 20
			Concrete: 10
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Eff: CreateEffect
		Explosions: large_napalm
		ImpactSounds: firebl3.aud
		Delay: 5
	-Warhead@3EffWater:
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch

ATMine:
	ValidTargets: Ground, Water, GroundActor, WaterActor
	Warhead@1Dam: SpreadDamage
		Spread: 256
		Damage: 40000
		AffectsParent: true
		ValidTargets: GroundActor, WaterActor
		InvalidTargets: Mine
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@2Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: mineblo1.aud
		ImpactActors: false
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		ValidTargets: Ground, GroundActor
		InvalidTargets: Structure, Wall

APMine:
	Inherits: ATMine
	Warhead@1Dam: SpreadDamage
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@2Eff: CreateEffect
		Explosions: napalm
		ImpactSounds: mine1.aud
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch

OreExplosion:
	ValidTargets: Ground, Water, GroundActor, WaterActor
	Warhead@1Dam: SpreadDamage
		Spread: 9
		Damage: 1000
		ValidTargets: GroundActor, WaterActor
		Versus:
			None: 90
			Wood: 70
			Light: 60
			Heavy: 20
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@3Res: CreateResource
		AddsResourceType: Ore
		Size: 1,1
	Warhead@2Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: kaboom25.aud
		ImpactActors: false

CrateNuke:
	ValidTargets: Ground, GroundActor, Trees, Water, WaterActor, Underwater, Air, AirborneActor
	Warhead@1Dam_impact: SpreadDamage
		Spread: 1c0
		Damage: 10000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: GroundActor, Trees, WaterActor, AirborneActor
		Versus:
			Concrete: 25
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Res_impact: DestroyResource
	Warhead@3Eff_impact: CreateEffect
		Explosions: nuke
		ImpactSounds: kaboom1.aud
		ImpactActors: false
	Warhead@4Dam_areanuke1: SpreadDamage
		Spread: 1c0
		Damage: 6000
		Falloff: 1000, 600, 400, 250, 150, 100, 0
		Delay: 5
		ValidTargets: GroundActor, Trees, WaterActor, AirborneActor
		Versus:
			Tree: 200
			Concrete: 25
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@5Res_areanuke1: DestroyResource
		Size: 4
		Delay: 5
	Warhead@6Eff_areanuke1: CreateEffect
		ImpactSounds: kaboom22.aud
		Delay: 5
		ImpactActors: false
	Warhead@6Smu_areanuke1: LeaveSmudge
		SmudgeType: Scorch
		ValidTargets: Ground, Infantry
		Size: 4
		Delay: 5
	Warhead@7FlashEffect: FlashEffect
		Duration: 20
		FlashType: Nuke

MiniNuke:
	ValidTargets: Ground, GroundActor, Trees, Water, WaterActor, Underwater, Air, AirborneActor
	Warhead@1Dam_impact: SpreadDamage
		Spread: 1c0
		Damage: 15000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: GroundActor, Trees, WaterActor, Underwater, AirborneActor
		Versus:
			Wood: 25
			Concrete: 25
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Res_impact: DestroyResource
		Size: 1
	Warhead@3Eff_impact: CreateEffect
		Explosions: nuke
		ImpactSounds: kaboom1.aud
		ImpactActors: false
	Warhead@4Dam_areanuke1: SpreadDamage
		Spread: 2c0
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: GroundActor, Trees, WaterActor, Underwater, AirborneActor
		Versus:
			Wood: 50
			Concrete: 25
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@5Res_areanuke1: DestroyResource
		Size: 2
		Delay: 5
	Warhead@6Eff_areanuke1: CreateEffect
		ImpactSounds: kaboom22.aud
		Delay: 5
		ImpactActors: false
	Warhead@7Dam_areanuke2: SpreadDamage
		Spread: 3c0
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 10
		ValidTargets: GroundActor, Trees, WaterActor, Underwater, AirborneActor
		Versus:
			Wood: 50
			Tree: 200
			Concrete: 25
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@9Res_areanuke2: DestroyResource
		Size: 3
		Delay: 10
	Warhead@10Dam_areanuke3: SpreadDamage
		Spread: 4c0
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 15
		ValidTargets: GroundActor, Trees, WaterActor, Underwater
		Versus:
			Tree: 300
			Concrete: 25
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@12Res_areanuke3: DestroyResource
		Size: 4
		Delay: 15
	Warhead@13Smu_areanuke3: LeaveSmudge
		SmudgeType: Scorch
		ValidTargets: Ground, Infantry
		Size: 4
		Delay: 15
	Warhead@14FlashEffect: FlashEffect
		Duration: 20
		FlashType: Nuke
