FPWR:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 640,-384,0, 640,512,0, -710,-512,0, -710,512,0
	Buildable:
		BuildPaletteOrder: 870
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.infonly
		Description: actor-fpwr.description
		Icon: fake-icon
	Tooltip:
		Name: actor-fpwr.name
		GenericName: actor-fpwr.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 40000
	Armor:
		Type: Wood
	WithBuildingBib:
	RenderSprites:
		Image: POWR
	Valued:
		Cost: 30

TENF:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	Buildable:
		BuildPaletteOrder: 880
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.infonly
		Description: actor-tenf.description
		Icon: fake-icon
	Tooltip:
		Name: actor-tenf.name
		GenericName: actor-tenf.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	RenderSprites:
		Image: TENT
	Valued:
		Cost: 50
	Health:
		HP: 60000
	Armor:
		Type: Wood
	WithBuildingBib:
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-512,0, 355,512,0, -281,-512,0, -630,512,0

SYRF:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Selectable:
		Bounds: 3072, 2048
	Buildable:
		BuildPaletteOrder: 890
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.medium
		Description: actor-syrf.description
		Icon: fake-icon
	Tooltip:
		Name: actor-syrf.name
		GenericName: actor-syrf.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Targetable:
		TargetTypes: WaterActor, Structure, SpyInfiltrate
	Building:
		Footprint: XXX xxx XXX
		Dimensions: 3,3
		TerrainTypes: Water
	RequiresBuildableArea:
		Adjacent: 8
	RenderSprites:
		Image: SYRD
	Valued:
		Cost: 100
	Health:
		HP: 100000
	Armor:
		Type: Light
	MapEditorData:
		ExcludeTilesets: INTERIOR
	HitShape:
		TargetableOffsets: 768,0,0, 768,-1024,0, 768,1024,0
		Type: Rectangle
			TopLeft: -1536, -1152
			BottomRight: 1536, 598
	HitShape@BOTTOM:
		TargetableOffsets: -768,0,0
		Type: Rectangle
			TopLeft: -512, 598
			BottomRight: 512, 1110

SPEF:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Selectable:
		Bounds: 3072, 2048
	Targetable:
		TargetTypes: WaterActor, Structure, SpyInfiltrate
	Buildable:
		BuildPaletteOrder: 890
		Queue: Defense
		Prerequisites: ~disabled
		Description: actor-spef.description
		Icon: fake-icon
	Tooltip:
		Name: actor-spef.name
		GenericName: actor-spef.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Building:
		Footprint: XXX xxx XXX
		Dimensions: 3,3
		TerrainTypes: Water
	RequiresBuildableArea:
		Adjacent: 8
	RenderSprites:
		Image: SPEN
	Valued:
		Cost: 80
	Health:
		HP: 100000
	Armor:
		Type: Light
	MapEditorData:
		ExcludeTilesets: INTERIOR
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -598
			BottomRight: 1536, 598
	HitShape@TOPANDBOTTOM:
		TargetableOffsets: 811,0,0, -811,0,0
		Type: Rectangle
			TopLeft: -555, -1110
			BottomRight: 555, 1110

WEAF:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Inherits@shape: ^3x2Shape
	Selectable:
		Bounds: 3072, 2048
	Buildable:
		BuildPaletteOrder: 920
		Prerequisites: ~structures.france, ~techlevel.medium
		Queue: Defense
		Description: actor-weaf.description
		Icon: fake-icon
	Tooltip:
		Name: actor-weaf.name
		GenericName: actor-weaf.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	RenderSprites:
		Image: WEAP
	WithBuildingBib:
	WithProductionDoorOverlay:
		RequiresCondition: !build-incomplete
		Sequence: build-top
	Valued:
		Cost: 200
	Health:
		HP: 150000
	Armor:
		Type: Wood
	-ActorPreviewPlaceBuildingPreview:
	SequencePlaceBuildingPreview:
		Sequence: place
		SequenceAlpha: 0.65

DOMF:
	Inherits: ^FakeBuilding
	Inherits@IDISABLE: ^DisableOnLowPower
	Inherits@infiltrate: ^InfiltratableFake
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-384,0, 630,384,0, -700,-512,0, -700,512,0
	Tooltip:
		Name: actor-domf.name
		GenericName: actor-domf.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Buildable:
		BuildPaletteOrder: 930
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.medium
		Description: actor-domf.description
		Icon: fake-icon
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	WithBuildingBib:
	RenderSprites:
		Image: DOME
	Valued:
		Cost: 180
	Health:
		HP: 100000
	Armor:
		Type: Wood

FIXF:
	Inherits: ^FakeBuilding
	Selectable:
		Bounds: 2901, 1450, 0, 128
		DecorationBounds: 3072, 2048
	Buildable:
		BuildPaletteOrder: 940
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.medium
		Description: actor-fixf.description
		Icon: fake-icon
	Tooltip:
		Name: actor-fixf.name
		GenericName: actor-fixf.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Building:
		Footprint: _=_ xxx _=_
		Dimensions: 3,3
	Health:
		HP: 80000
	Armor:
		Type: Wood
	WithBuildingBib:
		HasMinibib: true
	RenderSprites:
		Image: FIX
	Valued:
		Cost: 120
	HitShape:
		TargetableOffsets: 840,0,0, 598,-640,0, 598,640,0, -1060,0,0, -768,-640,0, -768,640,0
		Type: Polygon
			Points: -1536,-300, -640,-811, 640,-811, 1536,-300, 1536,555, 640,1110, -640,1110, -1536,555

FAPW:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Inherits@shape: ^3x2Shape
	HitShape:
		TargetableOffsets: -355,-1024,0
	Buildable:
		BuildPaletteOrder: 950
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.medium
		Description: actor-fapw.description
		Icon: fake-icon
	Tooltip:
		Name: actor-fapw.name
		GenericName: actor-fapw.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Building:
		Footprint: xxx Xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 70000
	Armor:
		Type: Wood
	WithBuildingBib:
	RenderSprites:
		Image: APWR
	Selectable:
		Bounds: 3072, 2048
		DecorationBounds: 3072, 2901, 0, -426
	Valued:
		Cost: 50

ATEF:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Inherits@IDISABLE: ^DisableOnLowPower
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	Tooltip:
		Name: actor-atef.name
		GenericName: actor-atef.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Buildable:
		BuildPaletteOrder: 970
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.high
		Description: actor-atef.description
		Icon: fake-icon
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	WithBuildingBib:
	RenderSprites:
		Image: ATEK
	Valued:
		Cost: 150
	Health:
		HP: 40000
	Armor:
		Type: Wood

PDOF:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Inherits@IDISABLE: ^DisableOnLowPower
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	Tooltip:
		Name: actor-pdof.name
		GenericName: actor-pdof.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Buildable:
		BuildPaletteOrder: 980
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.unrestricted
		BuildLimit: 1
		Description: actor-pdof.description
		Icon: fake-icon
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	RenderSprites:
		Image: PDOX
	WithBuildingBib:
		HasMinibib: true
	Valued:
		Cost: 150
	Health:
		HP: 100000
	Armor:
		Type: Wood
	Explodes:
		DamageThreshold: 50

MSLF:
	Inherits: ^FakeBuilding
	Inherits@infiltrate: ^InfiltratableFake
	Inherits@IDISABLE: ^DisableOnLowPower
	Inherits@shape: ^2x1Shape
	Selectable:
		Bounds: 2048, 1024
	Tooltip:
		Name: actor-mslf.name
		GenericName: actor-mslf.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Buildable:
		BuildPaletteOrder: 990
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.unrestricted
		BuildLimit: 1
		Description: actor-mslf.description
		Icon: fake-icon
	Building:
		Footprint: xx
		Dimensions: 2,1
	RenderSprites:
		Image: MSLO
	Valued:
		Cost: 250
	Health:
		HP: 100000
	Armor:
		Type: Wood
	Explodes:
		DamageThreshold: 50

FACF:
	Inherits: ^FakeBuilding
	Selectable:
		Bounds: 3072, 3072
	Buildable:
		BuildPaletteOrder: 1000
		Queue: Defense
		Prerequisites: ~structures.france, ~techlevel.medium
		Description: actor-facf.description
		Icon: fake-icon
	Tooltip:
		Name: actor-facf.name
		GenericName: actor-facf.generic-name
		GenericVisibility: Enemy
		GenericStancePrefix: False
	Building:
		Footprint: xxX xxx XxX ===
		Dimensions: 3,4
		LocalCenterOffset: 0,-512,0
	WithBuildingBib:
	RenderSprites:
		Image: FACT
	Valued:
		Cost: 200
	Health:
		HP: 150000
	Armor:
		Type: Wood
	HitShape:
		TargetableOffsets: 1273,939,0, -980,-640,0, -980,640,0
		Type: Rectangle
			TopLeft: -1536, -1536
			BottomRight: 1536, 1536
