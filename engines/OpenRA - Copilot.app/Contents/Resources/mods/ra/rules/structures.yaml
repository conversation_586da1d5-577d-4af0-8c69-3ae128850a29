MSLO:
	Inherits: ^ScienceBuilding
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@shape: ^2x1Shape
	Selectable:
		Bounds: 2048, 1024
	Valued:
		Cost: 2500
	Tooltip:
		Name: actor-mslo.name
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 140
		Prerequisites: techcenter, ~techlevel.unrestricted
		BuildLimit: 1
		Description: actor-mslo.description
	Building:
		Footprint: xx
		Dimensions: 2,1
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	NukePower:
		PauseOnCondition: disabled
		Cursor: nuke
		Icon: abomb
		ChargeInterval: 13500
		Name: actor-mslo.nukepower-name
		Description: actor-mslo.nukepower-description
		BeginChargeSpeechNotification: AbombPrepping
		EndChargeSpeechNotification: AbombReady
		SelectTargetSpeechNotification: SelectTarget
		InsufficientPowerSpeechNotification: InsufficientPower
		IncomingSpeechNotification: AbombLaunchDetected
		SelectTargetTextNotification: notification-select-target
		InsufficientPowerTextNotification: notification-insufficient-power
		BeginChargeTextNotification: notification-abomb-prepping
		EndChargeTextNotification: notification-abomb-ready
		IncomingTextNotification: notification-abomb-launch-detected
		MissileWeapon: atomic
		MissileImage: atomic
		MissileDelay: 5
		SpawnOffset: 1c0,427,0
		DisplayTimerRelationships: Ally, Neutral, Enemy
		DisplayBeacon: True
		DisplayRadarPing: True
		BeaconPoster: atomicon
		CameraRange: 10c0
		ArrowSequence: arrow
		ClockSequence: clock
		CircleSequence: circles
		SupportPowerPaletteOrder: 70
	SupportPowerChargeBar:
	InfiltrateForSupportPowerReset:
		Types: SpyInfiltrate
		PlayerExperience: 10
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate
	Power:
		Amount: -150
	MustBeDestroyed:
		RequiredForShortGame: false
	WithSupportPowerActivationAnimation:
		RequiresCondition: !build-incomplete

GAP:
	Inherits: ^ScienceBuilding
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Valued:
		Cost: 800
	Tooltip:
		Name: actor-gap.name
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 110
		Prerequisites: atek, ~structures.allies, ~techlevel.high
		Description: actor-gap.description
	Selectable:
		Bounds: 1024, 1024
		DecorationBounds: 1024, 2048, 0, -512
	WithSpriteBody:
		PauseOnCondition: disabled
	Health:
		HP: 50000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	CreatesShroud:
		Range: 6c0
		RequiresCondition: !disabled
	RenderShroudCircle:
	Power:
		Amount: -60
	MustBeDestroyed:
		RequiredForShortGame: false
	-AcceptsDeliveredCash:
	Explodes:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	HitShape:
		Type: Rectangle
			TopLeft: -512, -512
			BottomRight: 512, 512

SPEN:
	Inherits: ^Building
	Inherits@PRIMARY: ^PrimaryBuilding
	Selectable:
		Bounds: 3072, 2048
	InfiltrateForSupportPower:
		Proxy: powerproxy.sonarpulse
		Types: SpyInfiltrate
		PlayerExperience: 10
	Valued:
		Cost: 800
	Tooltip:
		Name: actor-spen.name
	Buildable:
		Queue: Building
		BuildPaletteOrder: 50
		Prerequisites: anypower, ~structures.soviet, ~techlevel.low
		Description: actor-spen.description
	Targetable:
		TargetTypes: WaterActor, Structure, C4, DetonateAttack, SpyInfiltrate
	Building:
		Footprint: XXX xxx XXX
		Dimensions: 3,3
		TerrainTypes: Water
	RequiresBuildableArea:
		AreaTypes: building
		Adjacent: 8
	-GivesBuildableArea:
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 0,-213,0
		Facing: 384
		ExitCell: -1,2
		ProductionTypes: Submarine
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: 0,-213,0
		Facing: 640
		ExitCell: 3,2
		ProductionTypes: Submarine
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: 0,0,0
		Facing: 128
		ExitCell: 0,0
		ProductionTypes: Submarine
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: 0,0,0
		Facing: 896
		ExitCell: 2,0
		ProductionTypes: Submarine
	Exit@b1:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,1024,0
		Facing: 640
		ExitCell: 0,2
		ProductionTypes: Ship
	Exit@b2:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,1024,0
		Facing: 896
		ExitCell: 2,2
		ProductionTypes: Ship
	Exit@b3:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,-1024,0
		Facing: 384
		ExitCell: 0,0
		ProductionTypes: Ship
	Exit@b4:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,-1024,0
		Facing: 128
		ExitCell: 2,0
		ProductionTypes: Ship
	Production:
		Produces: Ship, Submarine
	-SpawnActorsOnSell:
	RepairsUnits:
		HpPerStep: 1000
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: notification-repairing
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: notification-unit-repaired
		PlayerExperience: 5
	RallyPoint:
		ForceSetType: Ship
	CommandBarBlacklist:
		DisableStop: false
	ProductionBar:
		ProductionType: Ship
	Power:
		Amount: -30
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 10c0
	RenderDetectionCircle:
	ProvidesPrerequisite@soviet:
		Factions: soviet, russia, ukraine
		Prerequisite: ships.soviet
	ProvidesPrerequisite@sovietvanilla:
		Factions: soviet
		Prerequisite: ships.sovietvanilla
	ProvidesPrerequisite@russia:
		Factions: russia
		Prerequisite: ships.russia
	ProvidesPrerequisite@ukraine:
		Factions: ukraine
		Prerequisite: ships.ukraine
	ProvidesPrerequisite@sovietstructure:
		RequiresPrerequisites: structures.soviet
		Prerequisite: ships.soviet
	ProvidesPrerequisite@sovietvanillastructure:
		RequiresPrerequisites: structures.sovietvanilla
		Prerequisite: ships.sovietvanilla
	ProvidesPrerequisite@russianstructure:
		RequiresPrerequisites: structures.russia
		Prerequisite: ships.russia
	ProvidesPrerequisite@ukrainianstructure:
		RequiresPrerequisites: structures.ukraine
		Prerequisite: ships.ukraine
	ProvidesPrerequisite@buildingname:
	MapEditorData:
		ExcludeTilesets: INTERIOR
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -598
			BottomRight: 1536, 598
	HitShape@TOPANDBOTTOM:
		TargetableOffsets: 811,0,0, -811,0,0
		Type: Rectangle
			TopLeft: -555, -1110
			BottomRight: 555, 1110

SYRD:
	Inherits: ^Building
	Inherits@PRIMARY: ^PrimaryBuilding
	Selectable:
		Bounds: 3072, 2048
	InfiltrateForSupportPower:
		Proxy: powerproxy.sonarpulse
		Types: SpyInfiltrate
		PlayerExperience: 10
	Buildable:
		Queue: Building
		BuildPaletteOrder: 40
		Prerequisites: anypower, ~structures.allies, ~techlevel.low
		Description: actor-syrd.description
	Valued:
		Cost: 1000
	Tooltip:
		Name: actor-syrd.name
	Targetable:
		TargetTypes: WaterActor, Structure, C4, DetonateAttack, SpyInfiltrate
	Building:
		Footprint: XXX xxx XXX
		Dimensions: 3,3
		TerrainTypes: Water
	RequiresBuildableArea:
		Adjacent: 8
	-GivesBuildableArea:
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,1024,0
		Facing: 640
		ExitCell: 0,2
		ProductionTypes: Ship, Boat
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,1024,0
		Facing: 896
		ExitCell: 2,2
		ProductionTypes: Ship, Boat
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,-1024,0
		Facing: 384
		ExitCell: 0,0
		ProductionTypes: Ship, Boat
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,-1024,0
		Facing: 128
		ExitCell: 2,0
		ProductionTypes: Ship, Boat
	Production:
		Produces: Ship, Boat
	-SpawnActorsOnSell:
	RepairsUnits:
		HpPerStep: 1000
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: notification-repairing
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: notification-unit-repaired
		PlayerExperience: 5
	RallyPoint:
		ForceSetType: Ship
	CommandBarBlacklist:
		DisableStop: false
	ProductionBar:
		ProductionType: Ship
	Power:
		Amount: -30
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 10c0
	RenderDetectionCircle:
	ProvidesPrerequisite@allies:
		Factions: allies, england, france, germany
		Prerequisite: ships.allies
	ProvidesPrerequisite@alliesvanilla:
		Factions: allies
		Prerequisite: ships.alliesvanilla
	ProvidesPrerequisite@england:
		Factions: england
		Prerequisite: ships.england
	ProvidesPrerequisite@france:
		Factions: france
		Prerequisite: ships.france
	ProvidesPrerequisite@germany:
		Factions: germany
		Prerequisite: ships.germany
	ProvidesPrerequisite@alliedstructure:
		RequiresPrerequisites: structures.allies
		Prerequisite: ships.allies
	ProvidesPrerequisite@alliedvanillastructure:
		RequiresPrerequisites: structures.alliesvanilla
		Prerequisite: ships.alliesvanilla
	ProvidesPrerequisite@englishstructure:
		RequiresPrerequisites: structures.england
		Prerequisite: ships.england
	ProvidesPrerequisite@frenchstructure:
		RequiresPrerequisites: structures.france
		Prerequisite: ships.france
	ProvidesPrerequisite@germanstructure:
		RequiresPrerequisites: structures.germany
		Prerequisite: ships.germany
	ProvidesPrerequisite@buildingname:
	MapEditorData:
		ExcludeTilesets: INTERIOR
	HitShape:
		TargetableOffsets: 768,0,0, 768,-1024,0, 768,1024,0
		Type: Rectangle
			TopLeft: -1536, -1152
			BottomRight: 1536, 598
	HitShape@BOTTOM:
		TargetableOffsets: -768,0,0
		Type: Rectangle
			TopLeft: -512, 598
			BottomRight: 512, 1110

IRON:
	Inherits: ^ScienceBuilding
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@shape: ^2x2Shape
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 130
		Prerequisites: stek, ~structures.soviet, ~techlevel.unrestricted
		BuildLimit: 1
		Description: actor-iron.description
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-iron.name
	Building:
		Footprint: ++ xx
		Dimensions: 2,2
	Selectable:
		Bounds: 2048, 2133, 0, -170
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
		HasMinibib: true
	GrantExternalConditionPower@IRONCURTAIN:
		PauseOnCondition: disabled
		Icon: invuln
		ChargeInterval: 3000
		Name: actor-iron.grantexternalconditionpower-ironcurtain-name
		Description: actor-iron.grantexternalconditionpower-ironcurtain-description
		Duration: 400
		SelectTargetSpeechNotification: SelectTarget
		InsufficientPowerSpeechNotification: InsufficientPower
		BeginChargeSpeechNotification: IronCurtainCharging
		EndChargeSpeechNotification: IronCurtainReady
		SelectTargetTextNotification: notification-select-target
		InsufficientPowerTextNotification: notification-insufficient-power
		BeginChargeTextNotification: notification-iron-curtain-charging
		EndChargeTextNotification: notification-iron-curtain-ready
		DisplayRadarPing: True
		Condition: invulnerability
		OnFireSound: ironcur9.aud
		SupportPowerPaletteOrder: 10
		Dimensions: 3, 3
		Footprint: _x_ xxx _x_
		BlockedCursor: move-blocked
	SupportPowerChargeBar:
	InfiltrateForSupportPowerReset:
		Types: SpyInfiltrate
		PlayerExperience: 10
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate
	Power:
		Amount: -200
	MustBeDestroyed:
		RequiredForShortGame: false

PDOX:
	Inherits: ^ScienceBuilding
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 120
		Prerequisites: atek, ~structures.allies, ~techlevel.unrestricted
		BuildLimit: 1
		Description: actor-pdox.description
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-pdox.name
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
		HasMinibib: true
	ProvidesPrerequisite@germany:
		Factions: germany
		Prerequisite: pdox.germany
	ProvidesPrerequisite@germanstructure:
		RequiresPrerequisites: structures.germany
		Prerequisite: pdox.germany
	ChronoshiftPower@chronoshift:
		OrderName: Chronoshift
		PauseOnCondition: disabled
		Prerequisites: !pdox.germany
		Icon: chrono
		ChargeInterval: 3000
		Name: actor-pdox.chronoshiftpower-chronoshift-name
		Description: actor-pdox.chronoshiftpower-chronoshift-description
		SelectTargetSpeechNotification: SelectTarget
		InsufficientPowerSpeechNotification: InsufficientPower
		BeginChargeSpeechNotification: ChronosphereCharging
		EndChargeSpeechNotification: ChronosphereReady
		SelectTargetTextNotification: notification-select-target
		InsufficientPowerTextNotification: notification-insufficient-power
		BeginChargeTextNotification: notification-chronosphere-charging
		EndChargeTextNotification: notification-chronosphere-ready
		Duration: 400
		KillCargo: true
		DisplayRadarPing: True
		SupportPowerPaletteOrder: 20
		Dimensions: 3, 3
		Footprint: _x_ xxx _x_
	ChronoshiftPower@advancedchronoshift:
		OrderName: AdvancedChronoshift
		PauseOnCondition: disabled
		Prerequisites: pdox.germany
		Icon: chrono
		ChargeInterval: 3000
		Name: actor-pdox.chronoshiftpower-advancedchronoshift-name
		Description: actor-pdox.chronoshiftpower-advancedchronoshift-description
		SelectTargetSpeechNotification: SelectTarget
		InsufficientPowerSpeechNotification: InsufficientPower
		BeginChargeSpeechNotification: ChronosphereCharging
		EndChargeSpeechNotification: ChronosphereReady
		SelectTargetTextNotification: notification-select-target
		InsufficientPowerTextNotification: notification-insufficient-power
		BeginChargeTextNotification: notification-chronosphere-charging
		EndChargeTextNotification: notification-chronosphere-charging
		Duration: 400
		KillCargo: true
		DisplayRadarPing: True
		SupportPowerPaletteOrder: 30
		Dimensions: 5, 5
		Footprint: __x__ _xxx_ xxxxx _xxx_ __x__
	SupportPowerChargeBar:
	InfiltrateForSupportPowerReset:
		Types: SpyInfiltrate
		PlayerExperience: 10
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate
	Power:
		Amount: -200
	MustBeDestroyed:
		RequiredForShortGame: false
	ProvidesPrerequisite@buildingname:

TSLA:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 80
		Prerequisites: weap, ~structures.soviet, ~techlevel.medium
		Description: actor-tsla.description
	Valued:
		Cost: 1200
	Tooltip:
		Name: actor-tsla.name
	Selectable:
		Bounds: 1024, 1024
		DecorationBounds: 1024, 1706, 0, -341
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 6c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	WithBuildingBib:
		HasMinibib: true
	WithTeslaChargeAnimation:
	Armament:
		Weapon: TeslaZap
		LocalOffset: 0,0,896
	AttackTesla:
		PauseOnCondition: disabled || build-incomplete
		ChargeAudio: tslachg2.aud
		MaxCharges: 3
		ReloadDelay: 120
	Power:
		Amount: -100
	DetectCloaked:
		Range: 6c0
		RequiresCondition: !disabled
	ProvidesPrerequisite@buildingname:

AGUN:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetAir
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 90
		Prerequisites: dome, ~structures.allies, ~techlevel.medium
		Description: actor-agun.description
	Valued:
		Cost: 800
	Tooltip:
		Name: actor-agun.name
	Selectable:
		Bounds: 1024, 1024
		DecorationBounds: 1024, 1365, 0, -170
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 60
		InitialFacing: 832
		RealignDelay: -1
		RequiresCondition: !build-incomplete
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
		Recoils: false
	Armament:
		Weapon: ZSU-23
		LocalOffset: 520,100,450, 520,-150,450
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: disabled || build-incomplete
	WithMuzzleOverlay:
	RenderRangeCircle:
		RangeCircleType: aa
	Power:
		Amount: -50
	-BodyOrientation:
	ClassicFacingBodyOrientation:

DOME:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-384,0, 630,384,0, -700,-512,0, -700,512,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 90
		Prerequisites: proc, ~techlevel.medium
		Description: actor-dome.description
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-dome.name
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Targetable:
		TargetTypes: GroundActor, Structure, C4, DetonateAttack, SpyInfiltrate
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 6c0
		Range: 10c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 6c0
		RequiresCondition: !disabled
	WithBuildingBib:
	ProvidesRadar:
		RequiresCondition: !jammed && !disabled
	InfiltrateForExploration:
		Types: SpyInfiltrate
		PlayerExperience: 10
	Power:
		Amount: -40
	ProvidesPrerequisite@buildingname:
	ExternalCondition@JAMMED:
		Condition: jammed

PBOX:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetAll
	Inherits@CARGOPIPS: ^CargoPips
	Tooltip:
		Name: actor-pbox.name
	Building:
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 40
		Prerequisites: tent, ~structures.allies, ~techlevel.low
		Description: actor-pbox.description
	Valued:
		Cost: 600
	CustomSellValue:
		Value: 400
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 512
	-QuantizeFacingsFromSequence:
	BodyOrientation:
		QuantizedFacings: 8
	InstantlyRepairable:
		RequiresCondition: damaged
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	Cargo:
		Types: Infantry
		MaxWeight: 1
		InitialUnits: e1
		PassengerConditions:
			e3: RocketSoldier
	WithRangeCircle@ROCKETSOLDIER:
		Color: FFFF0080
		Range: 5c0
		RequiresCondition: RocketSoldier
	-SpawnActorsOnSell:
	AttackGarrisoned:
		PauseOnCondition: build-incomplete
		Armaments: garrisoned
		PortOffsets: 384,0,128, 224,-341,128, -224,-341,128, -384,0,128, -224,341,128, 224,341,128
		PortYaws: 0, 176, 341, 512, 682, 853
		PortCones: 88, 88, 88, 88, 88, 88
	RenderRangeCircle:
		FallbackRange: 6c0
	Power:
		Amount: -20
	DetectCloaked:
		Range: 6c0

HBOX:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetAll
	Inherits@CARGOPIPS: ^CargoPips
	Tooltip:
		Name: actor-hbox.name
	Building:
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 50
		Prerequisites: tent, ~structures.allies, ~techlevel.medium
		Description: actor-hbox.description
	Valued:
		Cost: 750
	CustomSellValue:
		Value: 550
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Cloak:
		InitialDelay: 125
		CloakDelay: 60
		PauseOnCondition: cloak-force-disabled
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	Turreted:
		TurnSpeed: 512
	-QuantizeFacingsFromSequence:
	BodyOrientation:
		QuantizedFacings: 8
	InstantlyRepairable:
		RequiresCondition: damaged
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	Cargo:
		Types: Infantry
		MaxWeight: 1
		InitialUnits: e1
	-SpawnActorsOnSell:
	DetectCloaked:
		Range: 6c0
	RenderRangeCircle:
		FallbackRange: 6c0
	AttackGarrisoned:
		PauseOnCondition: build-incomplete
		Armaments: garrisoned
		PortOffsets: 384,0,128, 224,-341,128, -224,-341,128, -384,0,128, -224,341,128, 224,341,128
		PortYaws: 0, 176, 341, 512, 682, 853
		PortCones: 88, 88, 88, 88, 88, 88
	Power:
		Amount: -20
	-MustBeDestroyed:

GUN:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 70
		Prerequisites: tent, ~structures.allies, ~techlevel.medium
		Description: actor-gun.description
	Valued:
		Cost: 800
	Tooltip:
		Name: actor-gun.name
	Building:
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 5c0
		Range: 6c512
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 48
		InitialFacing: 192
		RealignDelay: -1
		RequiresCondition: !build-incomplete
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
		Recoils: false
	WithTurretAttackAnimation:
		Sequence: recoil
	Armament:
		Weapon: TurretGun
		LocalOffset: 512,0,112
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: build-incomplete
	WithMuzzleOverlay:
	Power:
		Amount: -40
	DetectCloaked:
		Range: 6c0
	-BodyOrientation:
	ClassicFacingBodyOrientation:

FTUR:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 60
		Prerequisites: barr, ~structures.soviet, ~techlevel.low
		Description: actor-ftur.description
	Valued:
		Cost: 600
	Tooltip:
		Name: actor-ftur.name
	Building:
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 512
		Offset: 0,0,112
	Armament:
		Weapon: FireballLauncher
		LocalOffset: 512,0,0
	AttackTurreted:
		PauseOnCondition: build-incomplete
	-QuantizeFacingsFromSequence:
	BodyOrientation:
		QuantizedFacings: 8
	Power:
		Amount: -20
	DetectCloaked:
		Range: 6c0
	ProvidesPrerequisite@buildingname:
	Explodes:
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode

SAM:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetAir
	Inherits@shape: ^2x1Shape
	Selectable:
		Bounds: 2048, 1024
	HitShape:
		Type: Rectangle
			TopLeft: -768,-512
			BottomRight: 768,512
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 100
		Prerequisites: dome, ~structures.soviet, ~techlevel.medium
		Description: actor-sam.description
	Valued:
		Cost: 700
	Tooltip:
		Name: actor-sam.name
	Building:
		Footprint: xx
		Dimensions: 2,1
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 5c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 120
		InitialFacing: 0
		RealignDelay: -1
		RequiresCondition: !build-incomplete
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
		Recoils: false
	Armament:
		Weapon: Nike
		LocalOffset: 0,0,320
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: disabled || build-incomplete
	WithMuzzleOverlay:
	RenderRangeCircle:
		RangeCircleType: aa
	Power:
		Amount: -40
	-BodyOrientation:
	ClassicFacingBodyOrientation:

ATEK:
	Inherits: ^ScienceBuilding
	Inherits@IDISABLE: ^DisableOnLowPower
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	Buildable:
		Queue: Building
		BuildPaletteOrder: 140
		Prerequisites: weap, dome, ~structures.allies, ~techlevel.high
		Description: actor-atek.description
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-atek.name
	ProvidesPrerequisite:
		Prerequisite: techcenter
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	GpsPower:
		PauseOnCondition: disabled
		Icon: gps
		OneShot: true
		ChargeInterval: 12000
		Name: actor-atek.gpspower-name
		Description: actor-atek.gpspower-description
		RevealDelay: 375
		LaunchSpeechNotification: SatelliteLaunched
		LaunchTextNotification: notification-satellite-launched
		DisplayTimerRelationships: Ally, Neutral, Enemy
		SupportPowerPaletteOrder: 90
	SupportPowerChargeBar:
	InfiltrateForSupportPowerReset:
		Types: SpyInfiltrate
		PlayerExperience: 10
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate
	Power:
		Amount: -200
	ProvidesPrerequisite@buildingname:

WEAP:
	Inherits: ^Building
	Inherits@shape: ^3x2Shape
	Inherits@PRIMARY: ^PrimaryBuilding
	Selectable:
		Bounds: 3072, 2048
	Buildable:
		Queue: Building
		BuildPaletteOrder: 80
		Prerequisites: proc, ~techlevel.low
		Description: actor-weap.description
	Valued:
		Cost: 2000
	Tooltip:
		Name: actor-weap.name
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	WithProductionDoorOverlay:
		RequiresCondition: !build-incomplete
		Sequence: build-top
	RallyPoint:
		ForceSetType: Vehicle
	CommandBarBlacklist:
		DisableStop: false
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 213,-128,0
		ExitCell: 1,2
	Production:
		Produces: Vehicle
	ProvidesPrerequisite@allies:
		Factions: allies, england, france, germany
		Prerequisite: vehicles.allies
	ProvidesPrerequisite@alliesvanilla:
		Factions: allies
		Prerequisite: vehicles.alliesvanilla
	ProvidesPrerequisite@england:
		Factions: england
		Prerequisite: vehicles.england
	ProvidesPrerequisite@france:
		Factions: france
		Prerequisite: vehicles.france
	ProvidesPrerequisite@germany:
		Factions: germany
		Prerequisite: vehicles.germany
	ProvidesPrerequisite@soviet:
		Factions: soviet, russia, ukraine
		Prerequisite: vehicles.soviet
	ProvidesPrerequisite@sovietvanilla:
		Factions: soviet
		Prerequisite: vehicles.sovietvanilla
	ProvidesPrerequisite@russia:
		Factions: russia
		Prerequisite: vehicles.russia
	ProvidesPrerequisite@ukraine:
		Factions: ukraine
		Prerequisite: vehicles.ukraine
	ProvidesPrerequisite@alliedstructure:
		RequiresPrerequisites: structures.allies
		Prerequisite: vehicles.allies
	ProvidesPrerequisite@alliedvanillastructure:
		RequiresPrerequisites: structures.alliesvanilla
		Prerequisite: vehicles.alliesvanilla
	ProvidesPrerequisite@englishstructure:
		RequiresPrerequisites: structures.england
		Prerequisite: vehicles.england
	ProvidesPrerequisite@frenchstructure:
		RequiresPrerequisites: structures.france
		Prerequisite: vehicles.france
	ProvidesPrerequisite@germanstructure:
		RequiresPrerequisites: structures.germany
		Prerequisite: vehicles.germany
	ProvidesPrerequisite@sovietstructure:
		RequiresPrerequisites: structures.soviet
		Prerequisite: vehicles.soviet
	ProvidesPrerequisite@sovietvanillastructure:
		RequiresPrerequisites: structures.sovietvanilla
		Prerequisite: vehicles.sovietvanilla
	ProvidesPrerequisite@russianstructure:
		RequiresPrerequisites: structures.russia
		Prerequisite: vehicles.russia
	ProvidesPrerequisite@ukrainianstructure:
		RequiresPrerequisites: structures.ukraine
		Prerequisite: vehicles.ukraine
	ProductionBar:
		ProductionType: Vehicle
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate
	InfiltrateForSupportPower:
		Proxy: vehicles.upgraded
		Types: SpyInfiltrate
		PlayerExperience: 10
	-ActorPreviewPlaceBuildingPreview:
	SequencePlaceBuildingPreview:
		Sequence: place
		SequenceAlpha: 0.65

FACT:
	Inherits: ^Building
	Selectable:
		Bounds: 3072, 3072
	Building:
		Footprint: xxX xxx XxX ===
		Dimensions: 3,4
		LocalCenterOffset: 0,-512,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 1000
		Prerequisites: ~disabled
		Description: actor-fact.description
	ProvidesPrerequisite@allies:
		Factions: allies, england, france, germany
		Prerequisite: structures.allies
	ProvidesPrerequisite@alliesvanilla:
		Factions: allies
		Prerequisite: structures.alliesvanilla
	ProvidesPrerequisite@england:
		Factions: england
		Prerequisite: structures.england
	ProvidesPrerequisite@france:
		Factions: france
		Prerequisite: structures.france
	ProvidesPrerequisite@germany:
		Factions: germany
		Prerequisite: structures.germany
	ProvidesPrerequisite@soviet:
		Factions: soviet, russia, ukraine
		Prerequisite: structures.soviet
	ProvidesPrerequisite@sovietvanilla:
		Factions: soviet
		Prerequisite: structures.sovietvanilla
	ProvidesPrerequisite@russia:
		Factions: russia
		Prerequisite: structures.russia
	ProvidesPrerequisite@ukraine:
		Factions: ukraine
		Prerequisite: structures.ukraine
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Production:
		Produces: Building, Defense
	Valued:
		Cost: 2000
	Tooltip:
		Name: actor-fact.name
	SpawnActorsOnSell:
		ActorTypes: e1,e1,e1,tecn,tecn2
		GuaranteedActorTypes: e1, e6
	BaseBuilding:
	Transforms:
		RequiresCondition: factundeploy
		PauseOnCondition: chrono-vortex || being-captured || being-demolished || build-incomplete
		IntoActor: mcv
		Offset: 1,1
		Facing: 384
	TransformsIntoMobile:
		RequiresCondition: factundeploy
		Locomotor: heavywheeled
		RequiresForceMove: true
	TransformsIntoPassenger:
		RequiresCondition: factundeploy
		CargoType: Vehicle
		RequiresForceMove: true
	TransformsIntoRepairable:
		RequiresCondition: factundeploy
		RepairActors: fix
		RequiresForceMove: true
	TransformsIntoTransforms:
		RequiresCondition: factundeploy && build-incomplete
	Sellable:
		RequiresCondition: !build-incomplete && !chrono-vortex && !being-captured && !being-demolished
	GrantConditionOnPrerequisite@GLOBALFACTUNDEPLOY:
		Condition: factundeploy
		Prerequisites: global-factundeploy
	ProductionBar@Building:
		ProductionType: Building
	ProductionBar@Defense:
		ProductionType: Defense
		Color: 8A8A8A
	BaseProvider:
		PauseOnCondition: being-captured
		Range: 16c0
	WithBuildingBib:
	WithBuildingPlacedAnimation:
		RequiresCondition: !build-incomplete && !chrono-vortex
	Power:
		Amount: 0
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
	HitShape:
		TargetableOffsets: 1273,939,0, -980,-640,0, -980,640,0
		Type: Rectangle
			TopLeft: -1536, -1536
			BottomRight: 1536, 1536
	ConyardChronoReturn:
		ReturnOriginalActorOnCondition: build-incomplete
		Condition: chrono-vortex
		Damage: 950
	TransferTimedExternalConditionOnTransform:
		Condition: invulnerability

PROC:
	Inherits: ^Building
	Buildable:
		Queue: Building
		BuildPaletteOrder: 60
		Prerequisites: anypower, ~techlevel.infonly
		Description: actor-proc.description
	Valued:
		Cost: 1400
	Tooltip:
		Name: actor-proc.name
	Building:
		Footprint: _X_ xxx X== ===
		Dimensions: 3,4
		LocalCenterOffset: 0,-512,0
	Selectable:
		Bounds: 3072, 2133, 0, 170
		DecorationBounds: 3072, 2986, 0, -85
	Targetable:
		TargetTypes: GroundActor, Structure, C4, DetonateAttack, ThiefInfiltrate, SpyInfiltrate
	Health:
		HP: 90000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Refinery:
	DockHost:
		Type: Unload
		DockAngle: 256
		DockOffset: 0, 1c0, 0
	StoresPlayerResources:
		Capacity: 2000
	CustomSellValue:
		Value: 300
	FreeActor:
		Actor: HARV
		SpawnOffset: 1,2
		Facing: 256
	InfiltrateForCash:
		Percentage: 50
		PlayerExperience: 5
		PlayerExperiencePercentage: 1
		Types: SpyInfiltrate, ThiefInfiltrate
		InfiltratedNotification: CreditsStolen
		InfiltratedTextNotification: notification-credits-stolen
	WithBuildingBib:
	WithIdleOverlay@TOP:
		RequiresCondition: !build-incomplete
		Sequence: idle-top
	Power:
		Amount: -30
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -512
			BottomRight: 1536, 598
	HitShape@TOP:
		TargetableOffsets: 1680,0,0
		Type: Rectangle
			TopLeft: -512, -1536
			BottomRight: 512, -512
	HitShape@BOTTOMLEFT:
		TargetableOffsets: -1260,-1024,0
		Type: Rectangle
			TopLeft: -1536, 598
			BottomRight: -512, 1280
	-ActorPreviewPlaceBuildingPreview:
	SequencePlaceBuildingPreview:
		Sequence: idle
		SequenceAlpha: 0.65
	WithResourceStoragePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 17
		FullSequence: pip-yellow

SILO:
	Inherits: ^Building
	Selectable:
		Bounds: 1024, 1024
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 35
		Prerequisites: proc, ~techlevel.infonly
		Description: actor-silo.description
	Valued:
		Cost: 150
	Targetable:
		TargetTypes: GroundActor, Structure, C4, DetonateAttack, ThiefInfiltrate
	Tooltip:
		Name: actor-silo.name
	-GivesBuildableArea:
	-MustBeDestroyed:
	Health:
		HP: 30000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	InfiltrateForCash:
		Percentage: 50
		PlayerExperience: 5
		PlayerExperiencePercentage: 1
		Types: ThiefInfiltrate
		InfiltratedNotification: CreditsStolen
		InfiltratedTextNotification: notification-credits-stolen
	WithBuildingBib:
		HasMinibib: true
	-WithSpriteBody:
	WithResourceLevelSpriteBody:
		Sequence: stages
	StoresPlayerResources:
		Capacity: 3000
	-SpawnActorsOnSell:
	Power:
		Amount: -10
	Explodes:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	WithResourceStoragePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 5
		FullSequence: pip-yellow

HPAD:
	Inherits: ^Building
	Inherits@shape: ^2x2Shape
	Inherits@PRIMARY: ^PrimaryBuilding
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 768,-512,0, 768,512,0, -281,-512,0, -630,512,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 120
		Prerequisites: dome, ~structures.allies, ~techlevel.medium
		Description: actor-hpad.description
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-hpad.name
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 80000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 0,-256,0
		ExitCell: 0,0
		Facing: 896
	RallyPoint:
		ForceSetType: Helicopter
	CommandBarBlacklist:
		DisableStop: false
	Production:
		Produces: Aircraft, Helicopter
	Reservable:
	ProductionBar:
		ProductionType: Aircraft
	Power:
		Amount: -10
	ProvidesPrerequisite@allies:
		Factions: allies, england, france, germany
		Prerequisite: aircraft.allies
	ProvidesPrerequisite@alliesvanilla:
		Factions: allies
		Prerequisite: aircraft.alliesvanilla
	ProvidesPrerequisite@england:
		Factions: england
		Prerequisite: aircraft.england
	ProvidesPrerequisite@france:
		Factions: france
		Prerequisite: aircraft.france
	ProvidesPrerequisite@germany:
		Factions: germany
		Prerequisite: aircraft.germany
	ProvidesPrerequisite@alliedstructure:
		RequiresPrerequisites: structures.allies
		Prerequisite: aircraft.allies
	ProvidesPrerequisite@alliedvanillastructure:
		RequiresPrerequisites: structures.alliesvanilla
		Prerequisite: aircraft.alliesvanilla
	ProvidesPrerequisite@englishstructure:
		RequiresPrerequisites: structures.england
		Prerequisite: aircraft.england
	ProvidesPrerequisite@frenchstructure:
		RequiresPrerequisites: structures.france
		Prerequisite: aircraft.france
	ProvidesPrerequisite@germanstructure:
		RequiresPrerequisites: structures.germany
		Prerequisite: aircraft.germany
	ProvidesPrerequisite@buildingname:
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate
	InfiltrateForSupportPower:
		Proxy: aircraft.upgraded
		Types: SpyInfiltrate
		PlayerExperience: 10

AFLD:
	Inherits: ^Building
	Inherits@shape: ^3x2Shape
	Inherits@PRIMARY: ^PrimaryBuilding
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 420,0,0, 420,-1024,0, 420,1024,0, -777,0,0, -777,-1024,0, -777,1024,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 130
		Prerequisites: dome, ~structures.soviet, ~techlevel.medium, ~!structures.ukraine
		Description: actor-afld.description
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-afld.name
	Selectable:
		Class: afld
		Bounds: 3072, 2048
	Building:
		Footprint: xxx xxx
		Dimensions: 3,2
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		ExitCell: 1,1
		Facing: 768
	RallyPoint:
		ForceSetType: Plane
	CommandBarBlacklist:
		DisableStop: false
	Production:
		Produces: Aircraft, Plane
	Reservable:
	ProvidesPrerequisite@soviet:
		Factions: soviet, russia, ukraine
		Prerequisite: aircraft.soviet
	ProvidesPrerequisite@sovietvanilla:
		Factions: soviet
		Prerequisite: aircraft.sovietvanilla
	ProvidesPrerequisite@russia:
		Factions: russia
		Prerequisite: aircraft.russia
	ProvidesPrerequisite@ukraine:
		Factions: ukraine
		Prerequisite: aircraft.ukraine
	ProvidesPrerequisite@sovietstructure:
		RequiresPrerequisites: structures.soviet
		Prerequisite: aircraft.soviet
	ProvidesPrerequisite@sovietvanillastructure:
		RequiresPrerequisites: structures.sovietvanilla
		Prerequisite: aircraft.sovietvanilla
	ProvidesPrerequisite@russianstructure:
		RequiresPrerequisites: structures.russia
		Prerequisite: aircraft.russia
	ProvidesPrerequisite@ukrainianstructure:
		RequiresPrerequisites: structures.ukraine
		Prerequisite: aircraft.ukraine
	AirstrikePower@spyplane:
		OrderName: SovietSpyPlane
		Prerequisites: aircraft.soviet
		Icon: spyplane
		ChargeInterval: 3750
		Name: actor-afld.airstrikepower-spyplane-name
		Description: actor-afld.airstrikepower-spyplane-description
		SelectTargetSpeechNotification: SelectTarget
		EndChargeSpeechNotification: SpyPlaneReady
		SelectTargetTextNotification: notification-select-target
		EndChargeTextNotification: notifcation-spy-plane-ready
		CameraActor: camera.spyplane
		CameraRemoveDelay: 150
		UnitType: u2
		QuantizedFacings: 8
		DisplayBeacon: true
		BeaconPoster: camicon
		ArrowSequence: arrow
		ClockSequence: clock
		CircleSequence: circles
		UseDirectionalTarget: True
		DirectionArrowAnimation: paradirection
		SupportPowerPaletteOrder: 60
	ParatroopersPower@paratroopers:
		OrderName: SovietParatroopers
		Prerequisites: aircraft.soviet
		Icon: paratroopers
		ChargeInterval: 7500
		Name: actor-afld.paratrooperspower-paratroopers-name
		Description: actor-afld.paratrooperspower-paratroopers-description
		DropItems: E1R1,E1R1,E1R1,E3R1,E3R1
		ReinforcementsArrivedSpeechNotification: ReinforcementsArrived
		SelectTargetSpeechNotification: SelectTarget
		ReinforcementsArrivedTextNotification: notification-reinforcements-have-arrived
		SelectTargetTextNotification: notification-select-target
		AllowImpassableCells: false
		QuantizedFacings: 8
		CameraActor: camera.paradrop
		DisplayBeacon: true
		BeaconPoster: pinficon
		ArrowSequence: arrow
		ClockSequence: clock
		CircleSequence: circles
		UseDirectionalTarget: True
		DirectionArrowAnimation: paradirection
		SupportPowerPaletteOrder: 50
	AirstrikePower@parabombs:
		OrderName: UkraineParabombs
		Prerequisites: aircraft.ukraine
		Icon: parabombs
		ChargeInterval: 7500
		Name: actor-afld.airstrikepower-parabombs-name
		Description: actor-afld.airstrikepower-parabombs-description
		SelectTargetSpeechNotification: SelectTarget
		SelectTargetTextNotification: notification-select-target
		CameraActor: camera
		CameraRemoveDelay: 150
		UnitType: badr.bomber
		QuantizedFacings: 8
		DisplayBeacon: true
		BeaconPoster: pbmbicon
		SquadSize: 1
		SquadOffset: 1792,1792,0
		ArrowSequence: arrow
		ClockSequence: clock
		CircleSequence: circles
		UseDirectionalTarget: True
		DirectionArrowAnimation: paradirection
		SupportPowerPaletteOrder: 40
	ProductionBar:
		ProductionType: Aircraft
	SupportPowerChargeBar:
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
		Prerequisite: afld
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate
	InfiltrateForSupportPower:
		Proxy: aircraft.upgraded
		Types: SpyInfiltrate
		PlayerExperience: 10
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete

AFLD.Ukraine:
	Inherits: AFLD
	Buildable:
		Prerequisites: dome, ~techlevel.medium, ~structures.ukraine
		Description: actor-afld-ukraine-description
	RenderSprites:
		Image: afld

POWR:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 640,-384,0, 640,512,0, -710,-512,0, -710,512,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 10
		Prerequisites: ~techlevel.infonly
		Description: actor-powr.description
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-powr.name
	ProvidesPrerequisite:
		Prerequisite: anypower
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 40000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
	Power:
		Amount: 100
	Targetable:
		TargetTypes: GroundActor, Structure, C4, DetonateAttack, SpyInfiltrate
	ScalePowerWithHealth:
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false

APWR:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@shape: ^3x2Shape
	HitShape:
		TargetableOffsets: -355,-1024,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 110
		Prerequisites: dome, ~techlevel.medium
		Description: actor-apwr.description
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-apwr.name
	ProvidesPrerequisite:
		Prerequisite: anypower
	Building:
		Footprint: xxx Xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Selectable:
		Bounds: 3072, 2048
		DecorationBounds: 3072, 2901, 0, -426
	Health:
		HP: 70000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 5c0
	WithBuildingBib:
	Power:
		Amount: 200
	Targetable:
		TargetTypes: GroundActor, Structure, C4, DetonateAttack, SpyInfiltrate
	ScalePowerWithHealth:
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false

STEK:
	Inherits: ^ScienceBuilding
	Inherits@shape: ^3x2Shape
	Selectable:
		Bounds: 3072, 2048
	HitShape:
		TargetableOffsets: 420,-768,0, 420,768,0, -770,-768,0, -770,768,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 150
		Prerequisites: weap, dome, ~structures.soviet, ~techlevel.high
		Description: actor-stek.description
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-stek.name
	ProvidesPrerequisite:
		Prerequisite: techcenter
	Building:
		Footprint: XxX XxX ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 80000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Power:
		Amount: -100
	ProvidesPrerequisite@buildingname:

BARR:
	Inherits: ^Building
	Inherits@shape: ^2x2Shape
	Inherits@PRIMARY: ^PrimaryBuilding
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 490,-470,0, 355,512,0, -355,-512,0, -630,512,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 30
		Prerequisites: anypower, ~structures.soviet, ~techlevel.infonly
		Description: actor-barr.description
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-barr.name
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	RallyPoint:
		ForceSetType: Infantry
	CommandBarBlacklist:
		DisableStop: false
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -170,810,0
		ExitCell: 1,2
		ProductionTypes: Soldier, Infantry
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: -725,640,0
		ExitCell: 0,2
		ProductionTypes: Soldier, Infantry
	Production:
		Produces: Infantry, Soldier
	GrantExternalConditionToProduced:
		Condition: produced
	ProductionBar:
		ProductionType: Infantry
	ProvidesPrerequisite:
		Prerequisite: barracks
	ProvidesPrerequisite@soviet:
		Factions: soviet, russia, ukraine
		Prerequisite: infantry.soviet
	ProvidesPrerequisite@sovietvanilla:
		Factions: soviet
		Prerequisite: infantry.sovietvanilla
	ProvidesPrerequisite@russia:
		Factions: russia
		Prerequisite: infantry.russia
	ProvidesPrerequisite@ukraine:
		Factions: ukraine
		Prerequisite: infantry.ukraine
	ProvidesPrerequisite@sovietstructure:
		RequiresPrerequisites: structures.soviet
		Prerequisite: infantry.soviet
	ProvidesPrerequisite@sovietvanillastructure:
		RequiresPrerequisites: structures.sovietvanilla
		Prerequisite: infantry.sovietvanilla
	ProvidesPrerequisite@russianstructure:
		RequiresPrerequisites: structures.russia
		Prerequisite: infantry.russia
	ProvidesPrerequisite@ukrainianstructure:
		RequiresPrerequisites: structures.ukraine
		Prerequisite: infantry.ukraine
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
	InfiltrateForSupportPower:
		Proxy: barracks.upgraded
		Types: SpyInfiltrate
		PlayerExperience: 10
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate

KENN:
	Inherits: ^Building
	Inherits@PRIMARY: ^PrimaryBuilding
	Selectable:
		Bounds: 1024, 1024
	Buildable:
		Queue: Building
		BuildPaletteOrder: 175
		Prerequisites: anypower, ~structures.soviet, ~techlevel.infonly
		Description: actor-kenn.description
	Valued:
		Cost: 200
	Tooltip:
		Name: actor-kenn.name
	-GivesBuildableArea:
	Health:
		HP: 30000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: True
	RallyPoint:
		ForceSetType: Dog
	CommandBarBlacklist:
		DisableStop: false
	Exit@0:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: -1,1
		ProductionTypes: Dog, Infantry
		Priority: 3
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: 0,1
		ProductionTypes: Dog, Infantry
		Priority: 2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: -1,0
		ProductionTypes: Dog, Infantry
		Priority: 2
	Exit@fallback1:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: -1,-1
		ProductionTypes: Dog, Infantry
	Exit@fallback2:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: 0,-1
		ProductionTypes: Dog, Infantry
	Exit@fallback3:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: 1,-1
		ProductionTypes: Dog, Infantry
	Exit@fallback4:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: 1,0
		ProductionTypes: Dog, Infantry
	Exit@fallback5:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: 1,1
		ProductionTypes: Dog, Infantry
	Production:
		Produces: Infantry, Dog
	ProductionBar:
		ProductionType: Infantry
	-SpawnActorsOnSell:
	Power:
		Amount: -10
	ProvidesPrerequisite@buildingname:

TENT:
	Inherits: ^Building
	Inherits@shape: ^2x2Shape
	Inherits@PRIMARY: ^PrimaryBuilding
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-512,0, 355,512,0, -281,-512,0, -630,512,0
	Buildable:
		Queue: Building
		BuildPaletteOrder: 20
		Prerequisites: anypower, ~structures.allies, ~techlevel.infonly
		Description: actor-tent.description
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-tent.name
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	RallyPoint:
		ForceSetType: Infantry
	CommandBarBlacklist:
		DisableStop: false
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -42,810,0
		ExitCell: 1,2
		ProductionTypes: Soldier, Infantry
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: -725,640,0
		ExitCell: 0,2
		ProductionTypes: Soldier, Infantry
	Production:
		Produces: Infantry, Soldier
	GrantExternalConditionToProduced:
		Condition: produced
	ProductionBar:
		ProductionType: Infantry
	ProvidesPrerequisite@barracks:
		Prerequisite: barracks
	ProvidesPrerequisite@allies:
		Factions: allies, england, france, germany
		Prerequisite: infantry.allies
	ProvidesPrerequisite@alliesvanilla:
		Factions: allies
		Prerequisite: infantry.alliesvanilla
	ProvidesPrerequisite@england:
		Factions: england
		Prerequisite: infantry.england
	ProvidesPrerequisite@france:
		Factions: france
		Prerequisite: infantry.france
	ProvidesPrerequisite@germany:
		Factions: germany
		Prerequisite: infantry.germany
	ProvidesPrerequisite@alliedstructure:
		RequiresPrerequisites: structures.allies
		Prerequisite: infantry.allies
	ProvidesPrerequisite@alliedvanillastructure:
		RequiresPrerequisites: structures.alliesvanilla
		Prerequisite: infantry.alliesvanilla
	ProvidesPrerequisite@englishstructure:
		RequiresPrerequisites: structures.england
		Prerequisite: infantry.england
	ProvidesPrerequisite@frenchstructure:
		RequiresPrerequisites: structures.france
		Prerequisite: infantry.france
	ProvidesPrerequisite@germanstructure:
		RequiresPrerequisites: structures.germany
		Prerequisite: infantry.germany
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
	InfiltrateForSupportPower:
		Proxy: barracks.upgraded
		Types: SpyInfiltrate
		PlayerExperience: 10
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, SpyInfiltrate

FIX:
	Inherits: ^Building
	Buildable:
		Queue: Building
		BuildPaletteOrder: 100
		Prerequisites: weap, ~techlevel.medium
		Description: actor-fix.description
	Valued:
		Cost: 1200
	Tooltip:
		Name: actor-fix.name
	Building:
		Footprint: _+_ +++ _+_
		Dimensions: 3,3
	Selectable:
		Bounds: 2901, 1450, 0, 128
		DecorationBounds: 3072, 2048
	Health:
		HP: 80000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Reservable:
	RallyPoint:
	CommandBarBlacklist:
		DisableStop: false
	RepairsUnits:
		HpPerStep: 1000
		Interval: 7
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: notification-repairing
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: notification-unit-repaired
		PlayerExperience: 5
	WithBuildingBib:
		HasMinibib: true
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	HitShape:
		TargetableOffsets: 840,0,0, 598,-640,0, 598,640,0, -1060,0,0, -768,-640,0, -768,640,0
		Type: Polygon
			Points: -1536,-300, -640,-811, 640,-811, 1536,-300, 1536,555, 640,1110, -640,1110, -1536,555

SBAG:
	Inherits: ^Wall
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 10
		Prerequisites: fact, ~structures.allies, ~techlevel.low
		Description: actor-sbag.description
	Valued:
		Cost: 30
	CustomSellValue:
		Value: 0
	Tooltip:
		Name: actor-sbag.name
	Health:
		HP: 15000
	Armor:
		Type: Wood
	LineBuild:
		NodeTypes: sandbag
	LineBuildNode:
		Types: sandbag
	WithWallSpriteBody:
		Type: sandbag

FENC:
	Inherits: ^Wall
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 20
		Prerequisites: fact, ~structures.soviet, ~techlevel.low
		Description: actor-fenc.description
	Valued:
		Cost: 30
	CustomSellValue:
		Value: 0
	Tooltip:
		Name: actor-fenc.name
	Health:
		HP: 15000
	Armor:
		Type: Wood
	LineBuild:
		NodeTypes: fence
	LineBuildNode:
		Types: fence
	WithWallSpriteBody:
		Type: fence

BRIK:
	Inherits: ^Wall
	Buildable:
		Queue: Defense
		BuildPaletteOrder: 30
		Prerequisites: fact, ~techlevel.medium
		Description: actor-brik.description
	Valued:
		Cost: 200
	CustomSellValue:
		Value: 0
	Tooltip:
		Name: actor-brik.name
	SoundOnDamageTransition:
		DamagedSounds: crmble2.aud
		DestroyedSounds: kaboom30.aud
	Health:
		HP: 40000
	Armor:
		Type: Concrete
	Crushable:
		CrushClasses: heavywall
	BlocksProjectiles:
	LineBuild:
		NodeTypes: concrete
	LineBuildNode:
		Types: concrete
	WithWallSpriteBody:
		Type: concrete

CYCL:
	Inherits: ^Wall
	Tooltip:
		Name: actor-cycl-name
	Armor:
		Type: Wood
	LineBuild:
		NodeTypes: chain
	LineBuildNode:
		Types: chain
	WithWallSpriteBody:
		Type: chain

BARB:
	Inherits: ^Wall
	Tooltip:
		Name: actor-barb-name
	Armor:
		Type: Wood
	LineBuild:
		NodeTypes: barbwire
	LineBuildNode:
		Types: barbwire
	WithWallSpriteBody:
		Type: barbwire

WOOD:
	Inherits: ^Wall
	Tooltip:
		Name: actor-wood-name
	Armor:
		Type: Wood
	LineBuild:
		NodeTypes: woodfence
	LineBuildNode:
		Types: woodfence
	WithWallSpriteBody:
		Type: woodfence

BARRACKS:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: actor-barracks-name

TECHCENTER:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: actor-techcenter-name

ANYPOWER:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: actor-anypower-name
