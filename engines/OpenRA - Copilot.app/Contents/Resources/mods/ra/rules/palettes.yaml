^Palettes:
	PaletteFromFile@terrain-snow:
		Name: terrain
		Tileset: SNOW
		Filename: snow.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-interior:
		Name: terrain
		Tileset: INTERIOR
		Filename: interior.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-temperat:
		Name: terrain
		Tileset: TEMPERAT
		Filename: temperat.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-desert:
		Name: terrain
		Tileset: DESERT
		Filename: desert.pal
		ShadowIndex: 3, 4
	PaletteFromFile@player:
		Name: player
		Filename: temperat.pal
		ShadowIndex: 4
	PaletteFromFile@player-noshadow:
		Name: player-noshadow
		Filename: temperat.pal
		TransparentIndex: 0, 4
	PaletteFromFile@chrome:
		Name: chrome
		Filename: temperat.pal
		ShadowIndex: 3
		AllowModifiers: false
	PaletteFromFile@cursor:
		Name: cursor
		Filename: temperat.pal
		AllowModifiers: false
		CursorPalette: true
	PaletteFromFile@effect:
		Name: effect
		Filename: temperat.pal
		ShadowIndex: 4
	ColorPickerPalette@colorpicker:
		Name: colorpicker
		BasePalette: player
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
		AllowModifiers: false
	PaletteFromFile@desert:
		Name: desert
		Filename: desert.pal
		ShadowIndex: 4
	PaletteFromRGBA@shadow:
		Name: shadow
		R: 0
		G: 0
		B: 0
		A: 140
	PaletteFromRGBA@moveflash:
		Name: moveflash
		R: 255
		G: 255
		B: 255
		A: 64
	ShroudPalette@shroud:
		Name: shroud
	ShroudPalette@fog:
		Name: fog
		Fog: true
	PlayerColorPalette:
		BasePalette: player
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
	PlayerColorPalette@NOSHADOW:
		BaseName: player-noshadow
		BasePalette: player-noshadow
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
	MenuPostProcessEffect:
	RotationPaletteEffect@defaultwater:
		Palettes: terrain
		ExcludeTilesets: DESERT
	RotationPaletteEffect@actorswater:
		Palettes: player, effect
	RotationPaletteEffect@desertwater:
		Palettes: terrain
		Tilesets: DESERT
		RotationBase: 32
	RotationPaletteEffect@desertwater-actor:
		Palettes: desert
		RotationBase: 32
	LightPaletteRotator:
		ExcludePalettes: terrain, effect, desert
	ChronoshiftPostProcessEffect:
	FlashPostProcessEffect@NUKE:
		Type: Nuke
	IndexedPalette@CIV2:
		Name: civilian2
		BasePalette: player
		Index: 7, 14, 118, 119, 159, 187, 188
		ReplaceIndex: 209, 12, 187, 188, 209, 167, 13
	IndexedPalette@CIV4:
		Name: civilian4
		BasePalette: player
		Index: 7, 109, 111, 206, 210
		ReplaceIndex: 187, 118, 119, 188, 182
	IndexedPalette@CIV5:
		Name: civilian5
		BasePalette: player
		Index: 7, 12, 109, 111, 200, 206, 210
		ReplaceIndex: 109, 131, 177, 178, 111, 111, 182
	IndexedPalette@CIV6:
		Name: civilian6
		BasePalette: player
		Index: 7, 14, 118, 119, 159
		ReplaceIndex: 120, 238, 236, 206, 111
	IndexedPalette@CIV7:
		Name: civilian7
		BasePalette: player
		Index: 14, 118, 119, 159, 187, 188
		ReplaceIndex: 131, 157, 212, 7, 118, 119
	IndexedPalette@CIV8:
		Name: civilian8
		BasePalette: player
		Index: 7, 14, 118, 119, 159, 187, 188, 200
		ReplaceIndex: 182, 131, 215, 7, 182, 198, 199, 111
	IndexedPalette@CIV9:
		Name: civilian9
		BasePalette: player
		Index: 14, 118, 119, 159, 187, 188
		ReplaceIndex: 7, 163, 165, 200, 111, 13
	IndexedPalette@CIV10:
		Name: civilian10
		BasePalette: player
		Index: 7, 14, 118, 119, 159, 187, 188
		ReplaceIndex: 137, 15, 129, 131, 137, 163, 165
