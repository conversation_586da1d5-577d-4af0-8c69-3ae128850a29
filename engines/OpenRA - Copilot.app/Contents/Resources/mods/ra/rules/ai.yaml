Player:
	ModularBot@RushAI:
		Name: Rush AI
		Type: rush
	ModularBot@NormalAI:
		Name: Normal AI
		Type: normal
	ModularBot@TurtleAI:
		Name: Turtle AI
		Type: turtle
	ModularBot@NavalAI:
		Name: Naval AI
		Type: naval
	GrantConditionOnBotOwner@rush:
		Condition: enable-rush-ai
		Bots: rush
	GrantConditionOnBotOwner@normal:
		Condition: enable-normal-ai
		Bots: normal
	GrantConditionOnBotOwner@turtle:
		Condition: enable-turtle-ai
		Bots: turtle
	GrantConditionOnBotOwner@naval:
		Condition: enable-naval-ai
		Bots: naval
	SupportPowerBotModule:
		RequiresCondition: enable-rush-ai || enable-normal-ai || enable-turtle-ai || enable-naval-ai
		Decisions:
			spyplane:
				OrderName: SovietSpyPlane
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			paratroopers:
				OrderName: SovietParatroopers
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			parabombs:
				OrderName: UkraineParabombs
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			nukepower:
				OrderName: NukePowerInfoOrder
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
	HarvesterBotModule:
		RequiresCondition: enable-rush-ai || enable-normal-ai || enable-turtle-ai || enable-naval-ai
		HarvesterTypes: harv
		RefineryTypes: proc
	BaseBuilderBotModule@rush:
		RequiresCondition: enable-rush-ai
		MinimumExcessPower: 60
		MaximumExcessPower: 160
		ExcessPowerIncrement: 40
		ExcessPowerIncreaseThreshold: 4
		ConstructionYardTypes: fact
		RefineryTypes: proc
		PowerTypes: powr,apwr
		BarracksTypes: barr,tent
		VehiclesFactoryTypes: weap
		ProductionTypes: barr,tent,weap
		SiloTypes: silo
		DefenseTypes: hbox,pbox,gun,ftur,tsla,agun,sam
		BuildingLimits:
			proc: 4
			barr: 1
			tent: 1
			kenn: 1
			dome: 1
			weap: 1
			atek: 1
			stek: 1
			fix: 1
		BuildingFractions:
			proc: 30
			barr: 1
			kenn: 1
			tent: 1
			weap: 1
			pbox: 7
			gun: 7
			tsla: 5
			gap: 2
			ftur: 10
			agun: 5
			sam: 5
			atek: 1
			stek: 1
			fix: 1
			dome: 10
			mslo: 1
		BuildingDelays:
			dome: 4500
	BaseBuilderBotModule@normal:
		RequiresCondition: enable-normal-ai
		MinimumExcessPower: 60
		MaximumExcessPower: 200
		ExcessPowerIncrement: 40
		ExcessPowerIncreaseThreshold: 4
		ConstructionYardTypes: fact
		RefineryTypes: proc
		PowerTypes: powr,apwr
		BarracksTypes: barr,tent
		VehiclesFactoryTypes: weap
		ProductionTypes: barr,tent,weap,afld,hpad
		NavalProductionTypes: spen, syrd
		SiloTypes: silo
		DefenseTypes: hbox,pbox,gun,ftur,tsla,agun,sam
		BuildingLimits:
			proc: 4
			barr: 1
			tent: 1
			dome: 1
			weap: 1
			spen: 1
			syrd: 1
			hpad: 4
			afld: 4
			afld.ukraine: 4
			atek: 1
			stek: 1
			fix: 1
		BuildingFractions:
			proc: 15
			tent: 1
			barr: 1
			kenn: 1
			dome: 1
			weap: 6
			hpad: 4
			spen: 1
			syrd: 1
			afld: 4
			afld.ukraine: 4
			pbox: 7
			gun: 7
			ftur: 10
			tsla: 5
			gap: 2
			fix: 1
			agun: 5
			sam: 1
			atek: 1
			stek: 1
			mslo: 1
		BuildingDelays:
			dome: 3000
	BaseBuilderBotModule@turtle:
		RequiresCondition: enable-turtle-ai
		MinimumExcessPower: 60
		MaximumExcessPower: 250
		ExcessPowerIncrement: 50
		ExcessPowerIncreaseThreshold: 4
		ConstructionYardTypes: fact
		RefineryTypes: proc
		PowerTypes: powr,apwr
		BarracksTypes: barr,tent
		VehiclesFactoryTypes: weap
		ProductionTypes: barr,tent,weap,afld,hpad
		NavalProductionTypes: spen, syrd
		SiloTypes: silo
		DefenseTypes: hbox,pbox,gun,ftur,tsla,agun,sam
		BuildingLimits:
			proc: 4
			barr: 1
			tent: 1
			kenn: 1
			dome: 1
			weap: 1
			spen: 1
			syrd: 1
			hpad: 4
			afld: 4
			afld.ukraine: 4
			atek: 1
			stek: 1
			fix: 1
		BuildingFractions:
			proc: 30
			tent: 1
			barr: 1
			kenn: 1
			weap: 3
			hpad: 2
			afld: 2
			afld.ukraine: 2
			spen: 1
			syrd: 1
			pbox: 10
			gun: 10
			ftur: 10
			tsla: 7
			gap: 3
			fix: 1
			dome: 10
			agun: 5
			sam: 5
			atek: 1
			stek: 1
			mslo: 1
		BuildingDelays:
			dome: 3000
	BaseBuilderBotModule@naval:
		RequiresCondition: enable-naval-ai
		MinimumExcessPower: 60
		MaximumExcessPower: 400
		ExcessPowerIncrement: 40
		ExcessPowerIncreaseThreshold: 4
		ConstructionYardTypes: fact
		RefineryTypes: proc
		PowerTypes: powr,apwr
		BarracksTypes: barr,tent
		VehiclesFactoryTypes: weap
		ProductionTypes: barr,tent,weap,afld,hpad
		NavalProductionTypes: spen, syrd
		SiloTypes: silo
		DefenseTypes: hbox,pbox,gun,ftur,tsla,agun,sam
		BuildingLimits:
			proc: 3
			dome: 1
			barr: 1
			tent: 1
			spen: 1
			syrd: 1
			hpad: 8
			afld: 8
			afld.ukraine: 8
			weap: 1
			atek: 1
			stek: 1
			fix: 1
		BuildingFractions:
			proc: 25
			dome: 1
			weap: 1
			hpad: 15
			afld: 15
			afld.ukraine: 15
			atek: 1
			stek: 1
			spen: 1
			syrd: 1
			fix: 1
			pbox: 2
			gun: 2
			ftur: 2
			tsla: 1
			agun: 5
			sam: 5
			mslo: 1
		BuildingDelays:
			dome: 3000
			fix: 20000
			tsla: 21000
	BuildingRepairBotModule:
		RequiresCondition: enable-rush-ai || enable-normal-ai || enable-turtle-ai || enable-naval-ai
	SquadManagerBotModule@rush:
		RequiresCondition: enable-rush-ai
		SquadSize: 20
		NavalUnitsTypes: ss, msub, dd, ca, lst, pt
		ExcludeFromSquadsTypes: harv, mcv, dog, badr.bomber, u2
		ConstructionYardTypes: fact
		AirUnitsTypes: mig, yak, heli, hind, mh60
		AircraftTargetType: AirborneActor
		ProtectionTypes: harv, mcv, mslo, gap, spen, syrd, iron, pdox, tsla, agun, dome, pbox, hbox, gun, ftur, sam, atek, weap, fact, proc, silo, hpad, afld, afld.ukraine, powr, apwr, stek, barr, kenn, tent, fix, fpwr, tenf, syrf, spef, weaf, domf, fixf, fapw, atef, pdof, mslf, facf
		IgnoredEnemyTargetTypes: AirborneActor
	McvManagerBotModule:
		RequiresCondition: enable-rush-ai || enable-normal-ai || enable-turtle-ai || enable-naval-ai
		McvTypes: mcv
		ConstructionYardTypes: fact
		McvFactoryTypes: weap
	UnitBuilderBotModule@rush:
		RequiresCondition: enable-rush-ai
		UnitsToBuild:
			e1: 65
			e2: 15
			e3: 30
			e4: 15
			dog: 15
			shok: 15
			harv: 10
			apc: 30
			jeep: 20
			arty: 15
			v2rl: 40
			ftrk: 30
			1tnk: 50
			2tnk: 50
			3tnk: 50
			4tnk: 25
			ttnk: 25
			stnk: 5
		UnitLimits:
			dog: 4
			harv: 8
			jeep: 4
			ftrk: 4
	SquadManagerBotModule@normal:
		RequiresCondition: enable-normal-ai
		SquadSize: 40
		NavalUnitsTypes: ss, msub, dd, ca, lst, pt
		ExcludeFromSquadsTypes: harv, mcv, dog, badr.bomber, u2
		ConstructionYardTypes: fact
		NavalProductionTypes: spen, syrd
		AirUnitsTypes: mig, yak, heli, hind, mh60
		AircraftTargetType: AirborneActor
		ProtectionTypes: harv, mcv, mslo, gap, spen, syrd, iron, pdox, tsla, agun, dome, pbox, hbox, gun, ftur, sam, atek, weap, fact, proc, silo, hpad, afld, afld.ukraine, powr, apwr, stek, barr, kenn, tent, fix, fpwr, tenf, syrf, spef, weaf, domf, fixf, fapw, atef, pdof, mslf, facf
		IgnoredEnemyTargetTypes: AirborneActor
	UnitBuilderBotModule@normal:
		RequiresCondition: enable-normal-ai
		UnitsToBuild:
			e1: 65
			e2: 15
			e3: 30
			e4: 15
			dog: 15
			shok: 15
			harv: 15
			apc: 30
			jeep: 20
			arty: 15
			v2rl: 40
			ftrk: 30
			1tnk: 40
			2tnk: 50
			3tnk: 50
			4tnk: 25
			ttnk: 25
			stnk: 5
			heli: 30
			mh60: 30
			mig: 30
			yak: 30
			ss: 10
			msub: 10
			dd: 10
			ca: 10
			pt: 10
		UnitLimits:
			dog: 4
			harv: 8
			jeep: 4
			ftrk: 4
	SquadManagerBotModule@turtle:
		RequiresCondition: enable-turtle-ai
		SquadSize: 10
		NavalUnitsTypes: ss, msub, dd, ca, lst, pt
		ExcludeFromSquadsTypes: harv, mcv, dog, badr.bomber, u2, mnly
		ConstructionYardTypes: fact
		NavalProductionTypes: spen, syrd
		AirUnitsTypes: mig, yak, heli, hind, mh60
		AircraftTargetType: AirborneActor
		ProtectionTypes: harv, mcv, mslo, gap, spen, syrd, iron, pdox, tsla, agun, dome, pbox, hbox, gun, ftur, sam, atek, weap, fact, proc, silo, hpad, afld, afld.ukraine, powr, apwr, stek, barr, kenn, tent, fix, fpwr, tenf, syrf, spef, weaf, domf, fixf, fapw, atef, pdof, mslf, facf
		IgnoredEnemyTargetTypes: AirborneActor
	UnitBuilderBotModule@turtle:
		RequiresCondition: enable-turtle-ai
		UnitsToBuild:
			e1: 65
			e2: 15
			e3: 30
			e4: 15
			dog: 15
			shok: 15
			harv: 15
			apc: 30
			jeep: 20
			arty: 15
			v2rl: 40
			ftrk: 50
			1tnk: 50
			2tnk: 50
			3tnk: 50
			4tnk: 25
			ttnk: 25
			stnk: 10
			heli: 30
			mh60: 30
			mig: 30
			yak: 30
			ss: 10
			msub: 10
			dd: 10
			ca: 10
			pt: 10
			mnly: 2
		UnitLimits:
			dog: 4
			harv: 8
			jeep: 4
			ftrk: 4
			mnly: 2
	MinelayerBotModule@turtle:
		RequiresCondition: enable-turtle-ai
		MinelayingActorTypes: mnly
		IgnoredEnemyTargetTypes: Structure, Defense, AirborneActor
		UseEnemyLocationTargetTypes: Structure, Defense, AirborneActor
		AwayFromAlliedTargetTypes: Structure, Defense
		AwayFromEnemyTargetTypes: Structure, Defense
	SquadManagerBotModule@naval:
		RequiresCondition: enable-naval-ai
		SquadSize: 1
		ExcludeFromSquadsTypes: harv, mcv, dog, badr.bomber, u2
		NavalUnitsTypes: ss, msub, dd, ca, lst, pt
		ConstructionYardTypes: fact
		NavalProductionTypes: spen, syrd
		AirUnitsTypes: mig, yak, heli, hind, mh60
		AircraftTargetType: AirborneActor
		ProtectionTypes: harv, mcv, mslo, gap, spen, syrd, iron, pdox, tsla, agun, dome, pbox, hbox, gun, ftur, sam, atek, weap, fact, proc, silo, hpad, afld, afld.ukraine, powr, apwr, stek, barr, kenn, tent, fix, fpwr, tenf, syrf, spef, weaf, domf, fixf, fapw, atef, pdof, mslf, facf
		IgnoredEnemyTargetTypes: AirborneActor
	UnitBuilderBotModule@naval:
		RequiresCondition: enable-naval-ai
		UnitsToBuild:
			harv: 1
			ftrk: 40
			arty: 40
			v2rl: 40
			jeep: 40
			heli: 30
			mh60: 30
			mig: 15
			yak: 60
			ss: 10
			msub: 30
			dd: 30
			ca: 20
			pt: 10
		UnitLimits:
			harv: 4
			ftrk: 3
			arty: 1
			v2rl: 1
			jeep: 1
		UnitDelays:
			harv: 10000
