^ExistsInWorld:
	AppearsOnRadar:
	CombatDebugOverlay:
	GivesExperience:
		PlayerExperienceModifier: 1
	ScriptTriggers:
	RenderDebugState:

^SpriteActor:
	BodyOrientation:
	QuantizeFacingsFromSequence:
	RenderSprites:

^ClassicFacingSpriteActor:
	ClassicFacingBodyOrientation:
	QuantizeFacingsFromSequence:
	RenderSprites:

^1x1Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -512, -512
			BottomRight: 512, 512

^2x1Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -1024, -512
			BottomRight: 1024, 512

^2x2Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -1024, -1024
			BottomRight: 1024, 1024

^3x2Shape:
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -1536, -1024
			BottomRight: 1536, 1024

^GainsExperience:
	GainsExperience:
		LevelUpNotification: LevelUp
		LevelUpTextNotification: notification-unit-promoted
		Conditions:
			200: rank-veteran
			400: rank-veteran
			800: rank-veteran
			1600: rank-veteran
		LevelUpImage: crate-effects
	GrantCondition@RANK-ELITE:
		RequiresCondition: rank-veteran >= 4
		Condition: rank-elite
	DamageMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 95
	DamageMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 90
	DamageMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 85
	DamageMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 75
	FirepowerMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 105
	FirepowerMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 110
	FirepowerMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 120
	FirepowerMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 130
	SpeedMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 105
	SpeedMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 110
	SpeedMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 120
	SpeedMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 140
	ReloadDelayMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 95
	ReloadDelayMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 90
	ReloadDelayMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 85
	ReloadDelayMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 75
	ChangesHealth@ELITE:
		Step: 0
		PercentageStep: 5
		Delay: 100
		StartIfBelow: 100
		DamageCooldown: 125
		RequiresCondition: rank-elite
	WithDecoration@RANK-1:
		Image: rank
		Sequence: rank-veteran-1
		Palette: effect
		Position: BottomRight
		Margin: 5, 6
		ValidRelationships: Ally, Enemy, Neutral
		RequiresCondition: rank-veteran == 1
	WithDecoration@RANK-2:
		Image: rank
		Sequence: rank-veteran-2
		Palette: effect
		Position: BottomRight
		Margin: 5, 6
		ValidRelationships: Ally, Enemy, Neutral
		RequiresCondition: rank-veteran == 2
	WithDecoration@RANK-3:
		Image: rank
		Sequence: rank-veteran-3
		Palette: effect
		Position: BottomRight
		Margin: 5, 6
		ValidRelationships: Ally, Enemy, Neutral
		RequiresCondition: rank-veteran == 3
	WithDecoration@RANK-ELITE:
		Image: rank
		Sequence: rank-elite
		Palette: effect
		Position: BottomRight
		Margin: 5, 6
		ValidRelationships: Ally, Enemy, Neutral
		RequiresCondition: rank-elite

^InfantryExperienceHospitalOverrides:
	WithDecoration@RANK-1:
		BlinkInterval: 32
		BlinkPatterns:
			hospitalheal: On, Off
	WithDecoration@RANK-2:
		BlinkInterval: 32
		BlinkPatterns:
			hospitalheal: On, Off
	WithDecoration@RANK-3:
		BlinkInterval: 32
		BlinkPatterns:
			hospitalheal: On, Off
	WithDecoration@RANK-ELITE:
		BlinkInterval: 32
		BlinkPatterns:
			hospitalheal: On, Off

^IronCurtainable:
	WithColoredOverlay@IRONCURTAIN:
		RequiresCondition: invulnerability
	DamageMultiplier@IRONCURTAIN:
		RequiresCondition: invulnerability
		Modifier: 0
	TimedConditionBar:
		Condition: invulnerability
	ExternalCondition@INVULNERABILITY:
		Condition: invulnerability

^AutoTargetGround:
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything
		ValidTargets: Infantry, Vehicle, Ship, Underwater, Defense, Mine
		InvalidTargets: NoAutoTarget
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything
		ValidTargets: Infantry, Vehicle, Ship, Underwater, Structure, Defense, Mine
		InvalidTargets: NoAutoTarget

^AutoTargetGroundAssaultMove:
	Inherits: ^AutoTargetGround
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything && !assault-move
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything || assault-move
	AttackMove:
		AssaultMoveCondition: assault-move

^AutoTargetAir:
	AutoTarget:
	AutoTargetPriority@DEFAULT:
		ValidTargets: AirborneActor
		InvalidTargets: NoAutoTarget

^AutoTargetAll:
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything
		ValidTargets: Infantry, Vehicle, Ship, Underwater, AirborneActor, Defense, Mine
		InvalidTargets: NoAutoTarget
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything
		ValidTargets: Infantry, Vehicle, Ship, Underwater, AirborneActor, Structure, Defense, Mine
		InvalidTargets: NoAutoTarget

^AutoTargetAllAssaultMove:
	Inherits: ^AutoTargetAll
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything && !assault-move
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything || assault-move
	AttackMove:
		AssaultMoveCondition: assault-move

^PlayerHandicaps:
	HandicapFirepowerMultiplier:
	HandicapDamageMultiplier:
	HandicapProductionTimeMultiplier:

^GlobalBounty:
	GrantConditionOnPrerequisite@GLOBALBOUNTY:
		Condition: global-bounty
		Prerequisites: global-bounty
	GivesBounty:
		RequiresCondition: global-bounty

^Vehicle:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^IronCurtainable
	Inherits@3: ^ClassicFacingSpriteActor
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableCombatUnit
	Inherits@handicaps: ^PlayerHandicaps
	Huntable:
	OwnerLostAction:
		Action: Kill
	UpdatesPlayerStatistics:
	Mobile:
		PauseOnCondition: being-captured
		Locomotor: wheeled
		TurnSpeed: 20
	Selectable:
		Bounds: 1024, 1024
	Targetable:
		RequiresCondition: !parachute
		TargetTypes: GroundActor, Vehicle
	Targetable@REPAIR:
		RequiresCondition: !parachute && damaged
		TargetTypes: Repair
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	Repairable:
		RepairActors: fix
	Chronoshiftable:
	Passenger:
		CargoType: Vehicle
	AttackMove:
	HiddenUnderFog:
	ActorLostNotification:
		TextNotification: notification-unit-lost
	ProximityCaptor:
		Types: Vehicle
	GpsDot:
		String: Vehicle
	WithDamageOverlay:
	Guard:
	Guardable:
	Tooltip:
		GenericName: meta-vehicle-generic-name
	CaptureManager:
		BeingCapturedCondition: being-captured
	Capturable:
		Types: vehicle
		CancelActivity: True
	CaptureNotification:
		Notification: UnitStolen
		TextNotification: notification-unit-stolen
		LoseNotification: UnitLost
		LoseTextNotification: notification-unit-lost
	MustBeDestroyed:
	Voiced:
		VoiceSet: VehicleVoice
	Parachutable:
		FallRate: 26
		KilledOnImpassableTerrain: true
		ParachutingCondition: parachute
	Explodes:
		Weapon: UnitExplodeSmall
		EmptyWeapon: UnitExplodeSmall
	WithFacingSpriteBody:
	WithParachute:
		ShadowImage: parach-shadow
		ShadowSequence: idle
		Image: parach
		Sequence: idle
		OpeningSequence: open
		Offset: 0,0,200
		RequiresCondition: parachute
	HitShape:
	MapEditorData:
		Categories: Vehicle

^TrackedVehicle:
	Inherits: ^Vehicle
	Mobile:
		Locomotor: tracked

^Infantry:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^GainsExperience
	Inherits@3: ^InfantryExperienceHospitalOverrides
	Inherits@4: ^SpriteActor
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableCombatUnit
	Inherits@handicaps: ^PlayerHandicaps
	Huntable:
	OwnerLostAction:
		Action: Kill
		DeathTypes: DefaultDeath
	Health:
		HP: 2500
	Armor:
		Type: None
	RevealsShroud:
		Range: 4c0
	Mobile:
		Speed: 54
		AlwaysTurnInPlace: true
		Locomotor: foot
	Selectable:
		Bounds: 768, 853, 0, -256
		DecorationBounds: 512, 768, 0, -341
	Targetable:
		RequiresCondition: !parachute
		TargetTypes: GroundActor, Infantry, Disguise
	Targetable@HEAL:
		RequiresCondition: !parachute && damaged
		TargetTypes: Heal
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	QuantizeFacingsFromSequence:
		Sequence: stand
	WithInfantryBody:
	WithDeathAnimation:
		DeathTypes:
			DefaultDeath: 1
			BulletDeath: 2
			SmallExplosionDeath: 3
			ExplosionDeath: 4
			FireDeath: 5
			ElectricityDeath: 6
		CrushedSequence: die-crushed
	AttackMove:
	Passenger:
		CargoType: Infantry
		CargoCondition: disable-experience
	GainsExperienceMultiplier:
		Modifier: 0
		RequiresCondition: disable-experience
	HiddenUnderFog:
	ActorLostNotification:
		TextNotification: notification-unit-lost
	GpsDot:
		String: Infantry
	Crushable:
		CrushSound: squishy2.aud
	Guard:
	Guardable:
	Tooltip:
		GenericName: meta-infantry-generic-name
	ChangesHealth@HOSPITAL:
		Step: 500
		Delay: 100
		StartIfBelow: 100
		DamageCooldown: 125
		RequiresCondition: hospitalheal
	GrantConditionOnPrerequisite@HOSPITAL:
		Condition: hospital
		Prerequisites: hosp
	GrantConditionOnDamageState@HOSPITAL:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	GrantCondition@HOSPITAL:
		RequiresCondition: hospital && damaged
		Condition: hospitalheal
	WithDecoration@REDCROSS:
		Image: pips
		Sequence: medic
		Position: BottomRight
		Margin: 17, 4
		RequiresCondition: hospitalheal
		BlinkInterval: 32
		BlinkPattern: Off, On
	DeathSounds@NORMAL:
		DeathTypes: DefaultDeath, BulletDeath, SmallExplosionDeath, ExplosionDeath
	DeathSounds@BURNED:
		Voice: Burned
		DeathTypes: FireDeath
	DeathSounds@ZAPPED:
		Voice: Zapped
		DeathTypes: ElectricityDeath
	Parachutable:
		FallRate: 26
		KilledOnImpassableTerrain: true
		GroundCorpseSequence: corpse
		GroundImpactSound: squishy2.aud
		WaterImpactSound: splash9.aud
		WaterCorpseSequence: small_splash
		ParachutingCondition: parachute
	Cloneable:
		Types: Infantry
	Voiced:
		VoiceSet: GenericVoice
	WithParachute:
		ShadowImage: parach-shadow
		ShadowSequence: idle
		Image: parach
		Sequence: idle
		OpeningSequence: open
		Offset: 0,0,427
		RequiresCondition: parachute
	HitShape:
		Type: Circle
			Radius: 128
	MapEditorData:
		Categories: Infantry
	EdibleByLeap:
	DetectCloaked:
		DetectionTypes: Cloak
		Range: 1c0

^Soldier:
	Inherits: ^Infantry
	UpdatesPlayerStatistics:
	MustBeDestroyed:
	ProximityCaptor:
		Types: Infantry
	TakeCover:
		DamageModifiers:
			Prone50Percent: 50
		DamageTriggers: TriggerProne
		Duration: 50
	WithInfantryBody:
		IdleSequences: idle1,idle2
		StandSequences: stand,stand2
	AttackFrontal:
		FacingTolerance: 0

^CivInfantry:
	Inherits: ^Infantry
	Selectable:
		Class: CivInfantry
	Valued:
		Cost: 10
	Tooltip:
		Name: meta-civinfantry-name
		GenericVisibility: None
	RevealsShroud:
		Range: 3c0
	Passenger:
		CustomPipType: gray
	ProximityCaptor:
		Types: CivilianInfantry
	ScaredyCat:
	Voiced:
		VoiceSet: CivilianMaleVoice
	Wanders:
		MinMoveDelay: 150
		MaxMoveDelay: 750
	MapEditorData:
		Categories: Civilian infantry

^ArmedCivilian:
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Armament:
		Weapon: Pistol
	Armament@GARRISONED:
		Name: garrisoned
		Weapon: Pistol
	AttackFrontal:
		FacingTolerance: 0
	WithInfantryBody:
		DefaultAttackSequence: shoot

^Ship:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^GainsExperience
	Inherits@3: ^IronCurtainable
	Inherits@4: ^SpriteActor
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableCombatUnit
	Inherits@handicaps: ^PlayerHandicaps
	Huntable:
	OwnerLostAction:
		Action: Kill
	UpdatesPlayerStatistics:
	Mobile:
		Locomotor: naval
	Selectable:
		Bounds: 1024, 1024
	Targetable:
		TargetTypes: WaterActor, Ship
	Targetable@REPAIR:
		RequiresCondition: damaged
		TargetTypes: Repair
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	HiddenUnderFog:
	AttackMove:
	ActorLostNotification:
		Notification: NavalUnitLost
		TextNotification: notification-naval-unit-lost
	ProximityCaptor:
		Types: Ship
	Chronoshiftable:
	RepairableNear:
		RepairActors: spen, syrd
	GpsDot:
		String: Ship
	WithDamageOverlay:
	Explodes:
		Weapon: UnitExplodeShip
		EmptyWeapon: UnitExplodeShip
	Guard:
	Guardable:
	Tooltip:
		GenericName: meta-ship-generic-name
	MustBeDestroyed:
	MapEditorData:
		ExcludeTilesets: INTERIOR
		Categories: Naval
	Voiced:
		VoiceSet: VehicleVoice
	WithFacingSpriteBody:
	HitShape:

^Submarine:
	Inherits: ^Ship
	Targetable:
		TargetTypes: WaterActor, Ship, Submarine, Repair
		RequiresCondition: !underwater
	Targetable@UNDERWATER:
		TargetTypes: Underwater, Submarine
		RequiresCondition: underwater
	Cloak:
		DetectionTypes: Underwater
		InitialDelay: 0
		CloakDelay: 50
		CloakStyle: Color
		CloakSound: subshow1.aud
		UncloakSound: subshow1.aud
		CloakedCondition: underwater
		PauseOnCondition: cloak-force-disabled
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	-MustBeDestroyed:
	WithDamageOverlay:
		MinimumDamageState: Critical

^NeutralPlane:
	Inherits@1: ^ExistsInWorld
	Inherits@4: ^SpriteActor
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableCombatUnit
	Inherits@handicaps: ^PlayerHandicaps
	OwnerLostAction:
		Action: Kill
	Armor:
		Type: Light
	UpdatesPlayerStatistics:
	AppearsOnRadar:
		UseLocation: true
	Selectable:
		Bounds: 1024, 1024
	Aircraft:
		AirborneCondition: airborne
	Targetable@GROUND:
		RequiresCondition: !airborne
		TargetTypes: GroundActor, Vehicle
	Targetable@AIRBORNE:
		RequiresCondition: airborne
		TargetTypes: AirborneActor
	Targetable@REPAIR:
		RequiresCondition: !airborne && damaged
		TargetTypes: Repair
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	HiddenUnderFog:
		Type: GroundPosition
	AttackMove:
	Guard:
	Guardable:
	ActorLostNotification:
		Notification: AirUnitLost
		TextNotification: notification-airborne-unit-lost
	ProximityCaptor:
		Types: Plane
	EjectOnDeath:
		PilotActor: E1
		SuccessRate: 50
		EjectOnGround: false
		EjectInAir: true
		AllowUnsuitableCell: true
		ChuteSound: chute1.aud
	GpsDot:
		String: Plane
	Tooltip:
		GenericName: meta-neutralplane-generic-name
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
	WithFacingSpriteBody:
	MustBeDestroyed:
	Voiced:
		VoiceSet: GenericVoice
	HitShape:
	MapEditorData:
		Categories: Aircraft
	SpawnActorOnDeath:
		RequiresCondition: airborne
	Explodes:
		Weapon: UnitExplode
		RequiresCondition: !airborne
	CaptureManager:
	Capturable:
		Types: aircraft
		RequiresCondition: !airborne
	CaptureNotification:
		Notification: UnitStolen
		TextNotification: notification-unit-stolen
		LoseNotification: UnitLost
		LoseTextNotification: notification-unit-lost

^Plane:
	Inherits: ^NeutralPlane
	Inherits@2: ^GainsExperience
	Huntable:
	Repairable:
		RepairActors: fix

^Helicopter:
	Inherits: ^Plane
	Tooltip:
		GenericName: meta-helicopter-generic-name
	Aircraft:
		CanHover: True
		CruisingCondition: cruising
		WaitDistanceFromResupplyBase: 4c0
		TakeOffOnResupply: true
		VTOL: true
		LandableTerrainTypes: Clear, Rough, Road, Ore, Beach, Gems
		Crushes: crate, mine, infantry
		CanSlide: True
	GpsDot:
		String: Helicopter
	Hovers@CRUISING:
		RequiresCondition: cruising
	-BodyOrientation:
	ClassicFacingBodyOrientation:

^BasicBuilding:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^IronCurtainable
	Inherits@3: ^SpriteActor
	Inherits@shape: ^1x1Shape
	Inherits@bounty: ^GlobalBounty
	Inherits@selection: ^SelectableBuilding
	Inherits@handicaps: ^PlayerHandicaps
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure
	Building:
		Dimensions: 1,1
		Footprint: x
		TerrainTypes: Clear,Road
		RequiresBaseProvider: True
		BuildSounds: placbldg.aud, build5.aud
		UndeploySounds: cashturn.aud
	ActorPreviewPlaceBuildingPreview:
		PreviewAlpha: 0.65
	RequiresBuildableArea:
		AreaTypes: building
	SoundOnDamageTransition:
		DamagedSounds: kaboom1.aud
		DestroyedSounds: kaboom22.aud
	WithSpriteBody:
	Explodes:
		Type: Footprint
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode
	CaptureNotification:
		TextNotification: notification-structure-captured
	ShakeOnDeath:
	ProximityCaptor:
		Types: Building
	Guardable:
		Range: 3c0
	FrozenUnderFog:
	FrozenUnderFogUpdatedByGps:
	Tooltip:
		GenericName: meta-basicbuilding-generic-name
	Demolishable:
	MapEditorData:
		Categories: Building
	CommandBarBlacklist:

^Building:
	Inherits: ^BasicBuilding
	Huntable:
	OwnerLostAction:
		Action: Kill
	UpdatesPlayerStatistics:
	GivesBuildableArea:
		AreaTypes: building, fake
	RepairableBuilding:
		RepairStep: 700
		PlayerExperience: 5
		RepairingNotification: Repairing
	InstantlyRepairable:
	AcceptsDeliveredCash:
	WithMakeAnimation:
		Condition: build-incomplete
	CaptureManager:
		BeingCapturedCondition: being-captured
	Capturable:
		RequiresCondition: !build-incomplete
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	SpawnActorsOnSell:
		ActorTypes: e1,e1,e1,tecn,tecn2
		GuaranteedActorTypes: e1
	MustBeDestroyed:
		RequiredForShortGame: true
	GpsDot:
		String: Structure
	Demolishable:
		Condition: being-demolished
	Sellable:
		RequiresCondition: !build-incomplete && !being-captured && !being-demolished
		SellSounds: cashturn.aud
		Notification: StructureSold
	WithBuildingRepairDecoration:
		Image: allyrepair
		Sequence: repair
		Position: Center
		Palette: player
		IsPlayerPalette: True

^ScienceBuilding:
	Inherits: ^Building
	SpawnActorsOnSell:
		ActorTypes: e1,e1,e1,e1,tecn,tecn2,tecn,tecn2,tecn,tecn2,tecn,tecn2,tecn,tecn2,e6,e6,e6,e6,e6,c10,c10,c10,c10

^Defense:
	Inherits: ^Building
	Inherits@selection: ^SelectableCombatBuilding
	Selectable:
		Bounds: 1024, 1024
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, Defense
	MustBeDestroyed:
		RequiredForShortGame: false
	-GivesBuildableArea:
	-AcceptsDeliveredCash:
	RenderRangeCircle:
	Explodes:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	MapEditorData:
		Categories: Defense
	-CommandBarBlacklist:

^Wall:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@shape: ^1x1Shape
	Inherits@handicaps: ^PlayerHandicaps
	Interactable:
		Bounds: 1024, 1024
	OwnerLostAction:
		Action: ChangeOwner
	Building:
		Dimensions: 1,1
		Footprint: x
		BuildSounds: placbldg.aud
		TerrainTypes: Clear,Road
		UndeploySounds: cashturn.aud
	FootprintPlaceBuildingPreview:
		LineBuildFootprintAlpha: 0.65
	RequiresBuildableArea:
		AreaTypes: building
		Adjacent: 7
	SoundOnDamageTransition:
		DamagedSounds: sandbag2.aud
		DestroyedSounds: sandbag2.aud
	Crushable:
		CrushClasses: wall
	LineBuild:
		Range: 8
		NodeTypes: wall
	LineBuildNode:
		Types: wall
	Targetable:
		TargetTypes: GroundActor, DetonateAttack, Wall, NoAutoTarget
	-GivesExperience:
	RenderSprites:
		Palette: effect
	WithWallSpriteBody:
	Sellable:
		SellSounds: cashturn.aud
	Guardable:
	FrozenUnderFog:
	FrozenUnderFogUpdatedByGps:
	Health:
		HP: 10000
	RadarColorFromTerrain:
		Terrain: Wall
	AppearsOnMapPreview:
		Terrain: Wall
	MapEditorData:
		Categories: Wall

^TechBuilding:
	Inherits: ^BasicBuilding
	Huntable:
	Health:
		HP: 40000
	Armor:
		Type: Wood
	Tooltip:
		Name: meta-techbuilding-name
		GenericVisibility: None
	FrozenUnderFog:
	MapEditorData:
		Categories: Tech building

^FakeBuilding:
	Inherits: ^Building
	GivesBuildableArea:
		AreaTypes: fake
	RequiresBuildableArea:
		AreaTypes: fake
	Health:
		HP: 10000
	Explodes:
		Weapon: Demolish
		DamageThreshold: 70
	RevealsShroud:
		Range: 1c0
	WithDecoration@fake:
		Position: Top
		Margin: 0, 4
		RequiresSelection: false
		Image: pips
		Sequence: tag-fake
	-SpawnActorsOnSell:
	-MustBeDestroyed:
	MapEditorData:
		Categories: Fake

^InfiltratableFake:
	Targetable:
		TargetTypes: GroundActor, Structure, C4, DetonateAttack, SpyInfiltrate
	InfiltrateForDecoration:
		Types: SpyInfiltrate
		Position: Top
		Margin: 0, 4
		RequiresSelection: true
		Image: pips
		Sequence: tag-fake

^AmmoBox:
	Inherits: ^TechBuilding
	-Selectable:
	Health:
		HP: 1000
	Explodes:
		Weapon: UnitExplode
	Tooltip:
		Name: meta-ammobox-name
	Targetable:
		TargetTypes: GroundActor, C4, DetonateAttack, Structure, NoAutoTarget
	Armor:
		Type: Light
	MapEditorData:
		Categories: Decoration
	Interactable:
		Bounds: 1024, 1024

^CivBuilding:
	Inherits: ^TechBuilding
	RenderSprites:
		Palette: player
	MapEditorData:
		ExcludeTilesets: INTERIOR
		Categories: Civilian building
	Explodes:
		Weapon: SmallBuildingExplode
	Explodes@CIVPANIC:
		Weapon: CivPanicExplosion

^CivField:
	Inherits: ^CivBuilding
	-HitShape:
	-Health:
	-Explodes:
	-Explodes@CIVPANIC:
	-Selectable:
	-ScriptTriggers:
	Tooltip:
		Name: meta-civfield-name
	-Targetable:
	-Demolishable:
	MapEditorData:
		ExcludeTilesets: INTERIOR
	Interactable:

^CivHaystackOrIgloo:
	Inherits: ^CivField
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	-Tooltip:
	GrantConditionOnTileSet@WINTER:
		Condition: winter
		TileSets: SNOW
	Tooltip@WINTER:
		Name: meta-civhaystackorigloo.winter--name
		RequiresCondition: winter
		GenericVisibility: None
		ShowOwnerRow: false
	Tooltip@SUMMER:
		Name: meta-civhaystackorigloo.summer--name
		RequiresCondition: !winter
		GenericVisibility: None
		ShowOwnerRow: false

^Tree:
	Inherits@1: ^SpriteActor
	Inherits@shape: ^1x1Shape
	Interactable:
	Tooltip:
		Name: meta-tree-name
		ShowOwnerRow: false
	RenderSprites:
		Palette: terrain
	WithSpriteBody:
	Building:
		Footprint: x
		Dimensions: 1,1
	AppearsOnRadar:
	RadarColorFromTerrain:
		Terrain: Tree
	AppearsOnMapPreview:
		Terrain: Tree
	Health:
		HP: 50000
	Armor:
		Type: Tree
	Targetable:
		TargetTypes: Trees
	WithDamageOverlay@SmallBurn:
		DamageTypes: Incendiary
		Image: burn-s
		Palette: effect
		MinimumDamageState: Light
		MaximumDamageState: Medium
	WithDamageOverlay@MediumBurn:
		DamageTypes: Incendiary
		Image: burn-m
		Palette: effect
		MinimumDamageState: Medium
		MaximumDamageState: Heavy
	WithDamageOverlay@LargeBurn:
		DamageTypes: Incendiary
		Image: burn-l
		Palette: effect
		MinimumDamageState: Heavy
		MaximumDamageState: Dead
	HiddenUnderShroud:
	MapEditorData:
		ExcludeTilesets: INTERIOR
		Categories: Tree
	RequiresSpecificOwners:
		ValidOwnerNames: Neutral

^TreeHusk:
	Inherits@1: ^SpriteActor
	Interactable:
	RenderSprites:
		Palette: terrain
	AppearsOnRadar:
	RadarColorFromTerrain:
		Terrain: Tree
	AppearsOnMapPreview:
		Terrain: Tree
	Building:
		Footprint: x
		Dimensions: 1,1
	WithSpriteBody:
	Tooltip:
		Name: meta-treehusk-name
		ShowOwnerRow: false
	HiddenUnderShroud:
	MapEditorData:
		Categories: Tree
	RequiresSpecificOwners:
		ValidOwnerNames: Neutral

^Box:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: meta-box-name
	MapEditorData:
		-ExcludeTilesets:
		Categories: Decoration

^BasicHusk:
	Interactable:
	Health:
		HP: 28000
	Armor:
		Type: Heavy
	HiddenUnderFog:
		Type: CenterPosition
		AlwaysVisibleRelationships: None
	ScriptTriggers:
	WithFacingSpriteBody:
	HitShape:
	MapEditorData:
		Categories: Husk

^Husk:
	Inherits: ^BasicHusk
	Inherits@2: ^ClassicFacingSpriteActor
	Husk:
		AllowedTerrain: Clear, Rough, Road, Ore, Gems, Beach
		Locomotor: tracked
	WithIdleOverlay@Burns:
		Image: fire
		Sequence: 1
		IsDecoration: true
	ChangesHealth:
		Step: -200
		StartIfBelow: 101
		Delay: 8
	OwnerLostAction:
		Action: ChangeOwner
	CaptureManager:
	Capturable:
		Types: husk
	TransformOnCapture:
		ForceHealthPercentage: 15
	InfiltrateForTransform:
		Types: Husk
		ForceHealthPercentage: 15
	WithColoredOverlay@IDISABLE:
		Color: 000000B4
	Targetable:
		TargetTypes: GroundActor, Husk, NoAutoTarget
		RequiresForceFire: true
	Chronoshiftable:
	Tooltip:
		GenericName: meta-husk-generic-name

^PlaneHusk:
	Inherits: ^BasicHusk
	Inherits@2: ^SpriteActor
	Targetable:
		TargetTypes: AirborneActor, Husk, NoAutoTarget
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
	Tooltip:
		GenericName: meta-planehusk-generic-name
	Aircraft:
	FallsToEarth:
		Moves: True
		Velocity: 86
		Explosion: UnitExplodePlane
		MaximumSpinSpeed: 0
	-MapEditorData:
	RevealOnDeath:
		Duration: 60
		Radius: 4c0

^HelicopterHusk:
	Inherits: ^BasicHusk
	Inherits@2: ^ClassicFacingSpriteActor
	Targetable:
		TargetTypes: AirborneActor, Husk, NoAutoTarget
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
	Tooltip:
		GenericName: meta-helicopterhusk-generic-name
	Aircraft:
		CanHover: True
		VTOL: true
		CanSlide: True
	FallsToEarth:
		Explosion: UnitExplodeHeli
	-MapEditorData:
	RevealOnDeath:
		Duration: 60
		Radius: 4c0

^Bridge:
	Inherits@shape: ^1x1Shape
	AlwaysVisible:
	Tooltip:
		Name: meta-bridge-name
		ShowOwnerRow: false
	Targetable:
		TargetTypes: GroundActor, WaterActor, Bridge
		RequiresForceFire: true
	Building:
		Footprint: ____ ____
		Dimensions: 4,2
	Health:
		HP: 100000
	Armor:
		Type: Concrete
	ScriptTriggers:
	BodyOrientation:
		QuantizedFacings: 1
	Interactable:
		Bounds: 4096, 2048
	ShakeOnDeath:
		Duration: 15
		Intensity: 6

^Rock:
	Inherits@1: ^SpriteActor
	Interactable:
	Tooltip:
		Name: meta-rock-name
		ShowOwnerRow: false
	RenderSprites:
		Palette: desert
	WithSpriteBody:
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	AppearsOnRadar:
	RadarColorFromTerrain:
		Terrain: Tree
	AppearsOnMapPreview:
		Terrain: Tree
	HiddenUnderShroud:
	MapEditorData:
		RequireTilesets: DESERT
		Categories: Decoration
	RequiresSpecificOwners:
		ValidOwnerNames: Neutral

^DesertCivBuilding:
	Inherits: ^CivBuilding
	RenderSprites:
		Palette: desert
	MapEditorData:
		RequireTilesets: DESERT

^Crate:
	Inherits@1: ^SpriteActor
	Interactable:
		Bounds: 1024, 1024
	HiddenUnderFog:
	Tooltip:
		Name: meta-crate.name
		GenericName: meta-crate.generic-name
		ShowOwnerRow: false
	Crate:
		Duration: 4500
		TerrainTypes: Clear, Rough, Road, Ore, Beach, Water
	RenderSprites:
		Palette: effect
		Image: scrate
	WithCrateBody:
		XmasImages: xcratea, xcrateb, xcratec, xcrated
		LandSequence: land
		WaterSequence: water
	Parachutable:
		FallRate: 26
		KilledOnImpassableTerrain: false
		ParachutingCondition: parachute
	Passenger:
	WithParachute:
		Image: parach
		Sequence: idle
		OpeningSequence: open
		ShadowImage: parach-shadow
		ShadowSequence: idle
		RequiresCondition: parachute
	MapEditorData:
		Categories: System

^Mine:
	Inherits: ^SpriteActor
	Interactable:
		Bounds: 1024, 1024
	WithSpriteBody:
	HiddenUnderFog:
	Mine:
		CrushClasses: mine
		DetonateClasses: mine
		AvoidFriendly: false
		BlockFriendly: false
	Health:
		HP: 5000
		NotifyAppliedDamage: false
	Armor:
		Type: Light
	Cloak:
		CloakSound:
		UncloakSound:
		CloakStyle: None
		DetectionTypes: Mine
		InitialDelay: 0
	Tooltip:
		Name: meta-mine-name
	Targetable:
		TargetTypes: GroundActor, Mine
	Immobile:
		OccupiesSpace: true
	HitShape:
	MapEditorData:
		Categories: System

^DisableOnLowPower:
	WithColoredOverlay@IDISABLE:
		RequiresCondition: disabled
		Color: 000000B4
	GrantConditionOnPowerState@LOWPOWER:
		Condition: lowpower
		ValidPowerStates: Low, Critical
	GrantCondition@IDISABLE:
		RequiresCondition: lowpower
		Condition: disabled

^DisableOnLowPowerOrPowerDown:
	Inherits: ^DisableOnLowPower
	GrantCondition@IDISABLE:
		RequiresCondition: lowpower || powerdown
		Condition: disabled
	ToggleConditionOnOrder:
		DisabledSound: EnablePower
		EnabledSound: DisablePower
		Condition: powerdown
		OrderName: PowerDown
	WithDecoration@POWERDOWN:
		Image: poweroff
		Sequence: offline
		Palette: chrome
		RequiresCondition: powerdown
		Position: Center
		Offsets:
			repairing: 10, 0
	PowerMultiplier@POWERDOWN:
		RequiresCondition: powerdown
		Modifier: 0
	RepairableBuilding:
		RepairCondition: repairing
	WithBuildingRepairDecoration:
		Offsets:
			powerdown: -10, 0

^DisabledByPowerOutage:
	WithColoredOverlay@IDISABLE:
		RequiresCondition: disabled
		Color: 000000B4
	GrantCondition@IDISABLE:
		RequiresCondition: power-outage
		Condition: disabled
	AffectedByPowerOutage:
		Condition: power-outage
	InfiltrateForPowerOutage:
		Types: SpyInfiltrate
		PlayerExperience: 10
	Power:
		RequiresCondition: !disabled

^Selectable:
	Selectable:
	SelectionDecorations:
	WithSpriteControlGroupDecoration:
		Margin: -2, 0
	DrawLineToTarget:

^SelectableCombatUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 10
		PriorityModifiers: Ctrl

^SelectableSupportUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 8
		PriorityModifiers: Ctrl, Alt

^SelectableEconomicUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 6
		PriorityModifiers: Ctrl, Alt

^SelectableCombatBuilding:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 4

^SelectableBuilding:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 2

^CargoPips:
	WithCargoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		CustomPipSequences:
			gray: pip-gray
			yellow: pip-yellow
			blue: pip-blue
			red: pip-red

^PrimaryBuilding:
	PrimaryBuilding:
		PrimaryCondition: primary
		SelectionNotification: PrimaryBuildingSelected
		SelectionTextNotification: notification-primary-building-selected
	WithDecoration@primary:
		RequiresCondition: primary
		Position: Top
		Margin: 0, 4
		RequiresSelection: true
		Image: pips
		Sequence: tag-primary
