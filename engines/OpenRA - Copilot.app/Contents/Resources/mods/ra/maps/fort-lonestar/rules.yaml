World:
	CrateSpawner:
		InitialSpawnDelay: 0
		Maximum: 4
		SpawnInterval: 1000
		CrateActors: fortcrate
	StartingUnits@mcvonly:
		BaseActor: tent
	WeatherOverlay:
		WindTick: 150, 550
		UseSquares: false
		ScatterDirection: 0, 0
		Gravity: 15, 25
		SwingOffset: 0, 0
		SwingSpeed: 0, 0
		SwingAmplitude: 0, 0
		ParticleColors: 304074, 28386C, 202C60, 182C54
		LineTailAlphaValue: 150
		ParticleSize: 1, 1
	TintPostProcessEffect:
		Red: 0.75
		Green: 0.85
		Blue: 1.5
		Ambient: 0.45
	MusicPlaylist:
		BackgroundMusic: rain
	FlashPostProcessEffect@LIGHTNINGSTRIKE:
		Type: LightningStrike
	LuaScript:
		Scripts: campaign.lua, fort-lonestar.lua, fort-lonestar-AI.lua
	MapBuildRadius:
		AllyBuildRadiusCheckboxVisible: False
		BuildRadiusCheckboxVisible: False
	SpawnStartingUnits:
		DropdownVisible: False
	MapOptions:
		TechLevelDropdownLocked: True
		TechLevel: unrestricted
		TechLevelDropdownVisible: False
		ShortGameCheckboxLocked: True
		ShortGameCheckboxEnabled: False
		ShortGameCheckboxVisible: False
	ScriptLobbyDropdown@difficulty:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			hard: options-difficulty.hard4p
			normal: options-difficulty.normal3p
			easy: options-difficulty.easy2p
			veryeasy: options-difficulty.veryeasy1p
			tough: options-difficulty.tough
			endless: options-difficulty.endless
		Default: hard
		DisplayOrder: 5
	MapStartingLocations:
		SeparateTeamSpawnsCheckboxEnabled: False
		SeparateTeamSpawnsCheckboxLocked: True
		SeparateTeamSpawnsCheckboxVisible: False
	TimeLimitManager:
		TimeLimitLocked: True
		TimeLimitDropdownVisible: False

FORTCRATE:
	Inherits: ^Crate
	SupportPowerCrateAction@parabombs:
		SelectionShares: 30
		Proxy: powerproxy.parabombs
		Sequence: parabombs
	HealActorsCrateAction:
		SelectionShares: 30
		Sound: heal2.aud
		Sequence: heal
	GiveCashCrateAction:
		Amount: 400
		UseCashTick: true
		SelectionShares: 30
	GiveUnitCrateAction@e7:
		Units: e7
		SelectionShares: 10
	GrantExternalConditionCrateAction@ironcurtain:
		SelectionShares: 10
		Sequence: invuln
		Sound: ironcur9.aud
		Condition: invulnerability
		Duration: 1200
	ExplodeCrateAction@bigboom:
		Weapon: SCUD
		SelectionShares: 5
	GiveBaseBuilderCrateAction:
		SelectionShares: 0
		NoBaseSelectionShares: 1000
		Units: mobiletent
		ValidFactions: allies

Player:
	ClassicProductionQueue@Infantry:
		BuildDurationModifier: 250
	-EnemyWatcher:
	Shroud:
		FogCheckboxLocked: True
		FogCheckboxEnabled: True
		FogCheckboxVisible: False
		ExploredMapCheckboxLocked: True
		ExploredMapCheckboxEnabled: False
		ExploredMapCheckboxVisible: False
	PlayerResources:
		DefaultCashDropdownLocked: True
		DefaultCashDropdownVisible: False
		DefaultCash: 50
	-ModularBot@RushAI:
	-ModularBot@NormalAI:
	-ModularBot@NavalAI:
	-ModularBot@TurtleAI:
	DummyBot@LonestarAI:
		Name: Lonestar AI
		Type: lonestar
	LobbyPrerequisiteCheckbox@GLOBALFACTUNDEPLOY:
		Visible: False
^Infantry:
	Inherits@IC: ^IronCurtainable

^Husk:
	TransformOnCapture:
		ForceHealthPercentage: 80

OILB:
	Health:
		HP: 300000
	Armor:
		Type: Wood
	WithBuildingBib:
	RevealsShroud:
		Range: 3c0
	CashTrickler:
		Interval: 250
		Amount: 50

MOBILETENT:
	Inherits: ^Vehicle
	Inherits@selection: ^SelectableSupportUnit
	Valued:
		Cost: 2000
	Tooltip:
		Name: actor-mobiletent-name
	Selectable:
		DecorationBounds: 896, 896
	SelectionDecorations:
	Health:
		HP: 60000
	Armor:
		Type: Light
	Mobile:
		Speed: 85
		Locomotor: heavywheeled
	RevealsShroud:
		Range: 4c0
	MustBeDestroyed:
		RequiredForShortGame: true
	BaseBuilding:
	Transforms:
		IntoActor: tent
		Offset: 0,0
		Facing: 384
		TransformSounds: placbldg.aud, build5.aud
		NoTransformNotification: BuildingCannotPlaceAudio
		NoTransformTextNotification: notification-cannot-deploy-here
	RenderSprites:
		Image: TRUK

TENT:
	Health:
		HP: 100000
	Production:
		Produces: Infantry, Soldier, Dog, Defense
	-Sellable:
	Demolishable:
		-Condition:
	BaseProvider:
		Range: 12c0
	Power:
		Amount: 0
	ProductionBar@Defense:
		ProductionType: Defense
		Color: 8A8A8A
	BaseBuilding:

FTUR:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 400
	Power:
		Amount: 0
	GivesBuildableArea:
		AreaTypes: building

PBOX:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 400
	Health:
		HP: 20000
	Armor:
		Type: Heavy
	Power:
		Amount: 0
	GivesBuildableArea:
		AreaTypes: building

DOG:
	Buildable:
		Prerequisites: barracks
		BuildAtProductionType: Soldier
	Valued:
		Cost: 20

E1:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 20

E2:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 40
	Explodes:
		Chance: 20

E3:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 60

E4:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 100

E6:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 100

E7:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 750

3TNK:
	Armament:
		Weapon: TankNapalm
		Recoil: 200
		RecoilRecovery: 38

MEDI:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 100

SHOK:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 150

SNIPER:
	Inherits: ^Soldier
	Valued:
		Cost: 200
	Tooltip:
		Name: actor-sniper.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 80
		Prerequisites: barracks
		Description: actor-sniper.description
	Health:
		HP: 20000
	Passenger:
		CustomPipType: red
	RevealsShroud:
		Range: 6c0
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: ReturnFire
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry
	Armament@PRIMARY:
		Weapon: Sniper
	Armament@GARRISONED:
		Name: garrisoned
		Weapon: Sniper
		MuzzleSequence: garrison-muzzle
	WithInfantryBody:
		DefaultAttackSequence: shoot
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	Cloak:
		InitialDelay: 250
		CloakDelay: 120
		CloakSound:
		UncloakSound:
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Move
		PauseOnCondition: cloak-force-disabled
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	-MustBeDestroyed:
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded

SNIPER.soviets:
	Inherits: SNIPER
	Buildable:
		Prerequisites: ~disabled
	MustBeDestroyed:
	Targetable:
		TargetTypes: Disguise
	AutoTarget:
		InitialStanceAI: AttackAnything
	RenderSprites:
		Image: SNIPER

SPY:
	Buildable:
		BuildPaletteOrder: 60
		Prerequisites: barracks
	Valued:
		Cost: 300
	-MustBeDestroyed:

FTRK:
	-Armament@AA:
	-Armament@AG:
	Armament:
		Weapon: FLAK-23
		Recoil: 85
		LocalOffset: 512,0,192
		MuzzleSequence: muzzle

ARTY:
	Valued:
		Cost: 600
	Health:
		HP: 7500
	RevealsShroud:
		Range: 7c0

V2RL:
	Health:
		HP: 10000

4TNK:
	Health:
		HP: 250000
	Mobile:
		Speed: 56
	RevealsShroud:
		Range: 14c0
	Turreted:
		TurnSpeed: 4
	Armament@PRIMARY:
		Recoil: 8
		RecoilRecovery: 0c7
	Armament@SECONDARY:
		Recoil: 2
	Explodes:
		Weapon: napalm
		EmptyWeapon: napalm
	ChangesHealth:
		Step: 200
		Delay: 1
		StartIfBelow: 40

powerproxy.parabombs:
	AirstrikePower:
		Description: actor-powerproxy-parabombs-description
		CameraRemoveDelay: 50

BADR.Bomber:
	Health:
		HP: 6000
	Aircraft:
		Speed: 280
	AmmoPool:
		Ammo: 30
	Tooltip:
		Name: actor-mig-bomber-name
	SpawnActorOnDeath:
		Actor: MIG.Husk
	RenderSprites:
		Image: mig

MECH:
	Buildable:
		Prerequisites: barracks
	Valued:
		Cost: 1500

powerproxy.paratroopers:
	ParatroopersPower:
		DropItems: E1,E1,E1,E1,E2,E2

SILO:
	Buildable:
		Prerequisites: ~disabled

BRIK:
	Buildable:
		Prerequisites: ~disabled

HBOX:
	Buildable:
		Prerequisites: ~disabled

GUN:
	Buildable:
		Prerequisites: ~disabled

SAM:
	Buildable:
		Prerequisites: ~disabled

SBAG:
	Buildable:
		Prerequisites: ~disabled

FENC:
	Buildable:
		Prerequisites: ~disabled

MSLO:
	Buildable:
		Prerequisites: ~disabled

GAP:
	Buildable:
		Prerequisites: ~disabled

IRON:
	Buildable:
		Prerequisites: ~disabled

PDOX:
	Buildable:
		Prerequisites: ~disabled

AGUN:
	Buildable:
		Prerequisites: ~disabled

TSLA:
	Buildable:
		Prerequisites: ~disabled

THF:
	Buildable:
		Prerequisites: ~disabled
