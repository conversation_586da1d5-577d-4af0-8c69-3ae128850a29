Player:
	MissionObjectives:
		Cooperative: True
	PlayerResources:
		DefaultCash: 5000

World:
	MissionData:
		Briefing: One of our outposts is under heavy attack by a nearby Soviet base. <PERSON>, <PERSON> and many of our forces have been evacuated via helicopters, but those were shot down on the other side of the river. Fortunately they survived the crash, but are now in enemy territory. <PERSON> and the rest of the surviving forces need to fight their way out of there. The air defenses must be eliminated before another Search & Rescue helicopter can arrive.\n\nMeanwhile, the forces remaining at the base need to fend off the Soviet attacks.\n
	LuaScript:
		Scripts: campaign.lua, utils.lua, evacuation.lua
	ScriptLobbyDropdown@difficulty:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
		Default: normal
	TimeLimitManager:
		TimeLimitLocked: True

^Palettes:
	IndexedPlayerPalette:
		PlayerIndex:
			Allies: 224, 224, 225, 225, 226, 184, 185, 186, 187, 188, 188, 189, 190, 190, 191, 191
			Allies1: 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175
			Allies2: 208, 208, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 154, 155, 143
	IndexedPlayerPalette@NOSHADOW:
		PlayerIndex:
			Allies: 224, 224, 225, 225, 226, 184, 185, 186, 187, 188, 188, 189, 190, 190, 191, 191
			Allies1: 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175
			Allies2: 208, 208, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 154, 155, 143

E1:
	ScriptTags:

E2:
	ScriptTags:

DOG:
	ScriptTags:

3TNK:
	ScriptTags:

TRAN.Husk1:
	WithIdleOverlay@Burns:
		Image: fire
		Sequence: 1
		IsDecoration: True

TRAN.Husk2:
	WithIdleOverlay@Burns:
		Image: fire
		Sequence: 1
		IsDecoration: True

E7:
	Passenger:
		Weight: 0
	Buildable:
		Prerequisites: ~disabled

EINSTEIN:
	Passenger:
		Weight: 0

V01:
	SpawnActorOnDeath:
		Actor: healcrate

TRAN:
	-Selectable:
	Buildable:
		Prerequisites: ~disabled
	RevealsShroud:
		Range: 0c0
	Interactable:

2TNK:
	Buildable:
		Prerequisites: ~vehicles.allies

MECH:
	Buildable:
		Prerequisites: ~disabled

THF:
	Buildable:
		Prerequisites: ~disabled

SPEN:
	Buildable:
		Prerequisites: ~disabled

SYRD:
	Buildable:
		Prerequisites: ~disabled

TSLA:
	Buildable:
		Prerequisites: ~disabled

AGUN:
	Buildable:
		Prerequisites: ~disabled

SAM:
	Buildable:
		Prerequisites: ~disabled

ATEK:
	Buildable:
		Prerequisites: ~disabled

HPAD:
	Buildable:
		Prerequisites: ~disabled

AFLD:
	Buildable:
		Prerequisites: ~disabled

STEK:
	Buildable:
		Prerequisites: ~disabled

GAP:
	Buildable:
		Prerequisites: ~disabled

PDOX:
	Buildable:
		Prerequisites: ~disabled

IRON:
	Buildable:
		Prerequisites: ~disabled

MSLO:
	Buildable:
		Prerequisites: ~disabled

MIG:
	Buildable:
		Prerequisites: ~disabled

HELI:
	Buildable:
		Prerequisites: ~disabled

4TNK:
	Buildable:
		Prerequisites: ~disabled

MCV:
	Buildable:
		Prerequisites: ~disabled

ARTY:
	Buildable:
		Prerequisites: ~disabled

APC:
	Buildable:
		Prerequisites: ~disabled

MNLY:
	Buildable:
		Prerequisites: ~disabled

FTRK:
	Buildable:
		Prerequisites: ~disabled

MRJ:
	Buildable:
		Prerequisites: ~disabled

MGG:
	Buildable:
		Prerequisites: ~disabled

TTNK:
	Buildable:
		Prerequisites: ~disabled

QTNK:
	Buildable:
		Prerequisites: ~disabled

DTRK:
	Buildable:
		Prerequisites: ~disabled

CTNK:
	Buildable:
		Prerequisites: ~disabled

STNK:
	Buildable:
		Prerequisites: ~disabled

MSUB:
	Buildable:
		Prerequisites: ~disabled

Camera.SAM:
	Inherits: CAMERA
	RevealsShroud:
		Range: 2c0

powerproxy.paras1:
	Inherits: powerproxy.paratroopers
	ParatroopersPower:
		DropItems: E1,E1,E1,E2,3TNK

powerproxy.paras2:
	Inherits: powerproxy.paratroopers
	ParatroopersPower:
		DropItems: E1,E1,E1,E2,E2
