World:
	LuaScript:
		Scripts: campaign.lua, utils.lua, allies01.lua
	MissionData:
		Briefing: Rescue <PERSON> from the Headquarters inside this Soviet complex.\n\nOnce found, evacuate him via the helicopter at the signal flare.\n\n<PERSON><PERSON><PERSON> and <PERSON> must be kept alive at all costs.\n\nBeware the Soviet's Tesla Coils.\n\nDirect <PERSON> to destroy the westmost power plants to take them off-line.\n
		BackgroundVideo: prolog.vqa
		BriefingVideo: ally1.vqa
		StartVideo: landing.vqa
		WinVideo: snowbomb.vqa
		LossVideo: bmap.vqa

TRAN.Extraction:
	Inherits: TRAN
	RevealsShroud:
		Range: 0c0
	-RevealsShroud@GAPGEN:
	RejectsOrders:
	-Selectable:
	Cargo:
		Types: Einstein
		MaxWeight: 1
	RenderSprites:
		Image: tran
	Interactable:

TRAN.Insertion:
	Inherits: TRAN.Extraction
	Cargo:
		MaxWeight: 0

EINSTEIN:
	Passenger:
		CargoType: Einstein

C8:
	Inherits@2: ^ArmedCivilian

JEEP:
	Cargo:
		Types: Infantry, Einstein

TSLA:
	Power:
		Amount: -150
