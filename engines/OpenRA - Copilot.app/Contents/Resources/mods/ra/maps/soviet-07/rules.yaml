World:
	LuaScript:
		Scripts: campaign.lua, utils.lua, soviet07.lua
	MissionData:
		Briefing: The Allies have infiltrated one of our nuclear reactors! They have tampered with the core so that a meltdown is imminent within 30 minutes. They must not succeed!\n\nEnter the base and find any remaining technicians. Guide them to the 4 coolant stations so they can activate them, then activate the main computer. The security systems have been armed so beware.\n\nKill any Allies you find.
		BriefingVideo: soviet7.vqa
		StartVideo: countdwn.vqa
		WinVideo: averted.vqa
		LossVideo: nukestok.vqa
	ScriptLobbyDropdown@difficulty:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal

Player:
	LobbyPrerequisiteCheckbox@GLOBALBOUNTY:
		Enabled: False
		Locked: True

CAMERA:
	RevealsShroud:
		Range: 6c0

FTUR:
	Valued:
		Cost: 0
	Power:
		Amount: 0
	CaptureManager:
		-BeingCapturedCondition:
	-Sellable:
	Demolishable:
		-Condition:

PBOX:
	-AutoTarget:
	-AutoTargetPriority@DEFAULT:
	-AutoTargetPriority@ATTACKANYTHING:
