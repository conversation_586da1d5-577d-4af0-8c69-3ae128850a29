SS:
	Inherits: ^Submarine
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Ship
		BuildAtProductionType: Submarine
		BuildPaletteOrder: 30
		Prerequisites: ~spen, ~techlevel.low
		Description: actor-ss.description
	Valued:
		Cost: 950
	Tooltip:
		Name: actor-ss.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 25000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 20
		Speed: 78
	RevealsShroud:
		MinRange: 5c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Armament:
		Weapon: TorpTube
		LocalOffset: 0,-171,0, 0,171,0
		FireDelay: 2
	AttackFrontal:
		FacingTolerance: 0
	AutoTargetPriority@DEFAULT:
		ValidTargets: WaterActor, Underwater
		InvalidTargets: NoAutoTarget, Structure
	AutoTargetPriority@ATTACKANYTHING:
		ValidTargets: WaterActor, Underwater
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 5c0
	RenderDetectionCircle:
	Explodes:
		Weapon: UnitExplodeSubmarine
		EmptyWeapon: UnitExplodeSubmarine
	Selectable:
		DecorationBounds: 1621, 1621

MSUB:
	Inherits: ^Submarine
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Buildable:
		Queue: Ship
		BuildAtProductionType: Submarine
		BuildPaletteOrder: 60
		Prerequisites: ~spen, stek, ~techlevel.high
		Description: actor-msub.description
	Valued:
		Cost: 2000
	Tooltip:
		Name: actor-msub.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 40000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 12
		Speed: 44
	RevealsShroud:
		MinRange: 5c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Cloak:
		CloakDelay: 100
	Armament@PRIMARY:
		Weapon: SubMissile
		LocalOffset: 0,-171,0, 0,171,0
		FireDelay: 2
	Armament@SECONDARY:
		Weapon: SubMissileAA
		LocalOffset: 0,-171,0, 0,171,0
		FireDelay: 2
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		FacingTolerance: 0
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: ReturnFire
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
	Explodes:
		Weapon: UnitExplodeSubmarine
		EmptyWeapon: UnitExplodeSubmarine
	Selectable:
		DecorationBounds: 1877, 1877

DD:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Buildable:
		Queue: Ship
		BuildAtProductionType: Boat
		BuildPaletteOrder: 40
		Prerequisites: ~syrd, dome, ~techlevel.medium
		Description: actor-dd.description
	Valued:
		Cost: 1000
	Tooltip:
		Name: actor-dd.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 28
		Speed: 92
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 28
		Offset: 469,0,128
	Armament@PRIMARY:
		Weapon: Stinger
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 64, -64
	Armament@SECONDARY:
		Weapon: DepthCharge
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 80, -80
	Armament@TERTIARY:
		Weapon: StingerAA
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 64, -64
	AttackTurreted:
	WithSpriteTurret:
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
	Selectable:
		DecorationBounds: 1621, 1621

CA:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Ship
		BuildAtProductionType: Boat
		BuildPaletteOrder: 50
		Prerequisites: ~syrd, atek, ~techlevel.high
		Description: actor-ca.description
	Valued:
		Cost: 2400
	Tooltip:
		Name: actor-ca.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 80000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 12
		Speed: 44
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted@PRIMARY:
		Turret: primary
		Offset: -896,0,128
		TurnSpeed: 12
	Turreted@SECONDARY:
		Turret: secondary
		Offset: 768,0,128
		TurnSpeed: 12
	Armament@PRIMARY:
		Turret: primary
		Weapon: 8Inch
		LocalOffset: 480,-100,40, 480,100,40
		Recoil: 171
		RecoilRecovery: 34
		MuzzleSequence: muzzle
	Armament@SECONDARY:
		Name: secondary
		Turret: secondary
		Weapon: 8Inch
		LocalOffset: 480,-100,40, 480,100,40
		Recoil: 171
		RecoilRecovery: 34
		MuzzleSequence: muzzle
	AttackTurreted:
		Turrets: primary, secondary
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
	WithMuzzleOverlay:
	WithSpriteTurret@PRIMARY:
		Turret: primary
	WithSpriteTurret@SECONDARY:
		Turret: secondary
	Selectable:
		DecorationBounds: 1877, 1877

LST:
	Inherits: ^Ship
	Inherits@CARGOPIPS: ^CargoPips
	Buildable:
		Queue: Ship
		BuildPaletteOrder: 10
		Prerequisites: ~techlevel.low
		Description: actor-lst.description
	Valued:
		Cost: 700
	Tooltip:
		Name: actor-lst.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 35000
	Armor:
		Type: Heavy
	Mobile:
		Locomotor: lcraft
		Speed: 128
		PauseOnCondition: notmobile
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithLandingCraftAnimation:
		OpenTerrainTypes: Clear, Rough, Road, Ore, Gems, Beach
	Cargo:
		Types: Infantry, Vehicle
		MaxWeight: 5
		PassengerFacing: 0
		LoadingCondition: notmobile
	-Chronoshiftable:
	Selectable:
		DecorationBounds: 1536, 1536

PT:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Ship
		BuildAtProductionType: Boat
		BuildPaletteOrder: 20
		Prerequisites: ~syrd, ~techlevel.low
		Description: actor-pt.description
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-pt.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 20000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 28
		Speed: 142
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Turreted:
		TurnSpeed: 28
		Offset: 512,0,0
	Armament@PRIMARY:
		Weapon: 2Inch
		LocalOffset: 208,0,48
		MuzzleSequence: muzzle
	Armament@SECONDARY:
		Name: secondary
		Weapon: DepthCharge
		MuzzleSequence: muzzle
	AttackTurreted:
	WithMuzzleOverlay:
	WithSpriteTurret:
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
	Selectable:
		DecorationBounds: 1536, 1536
