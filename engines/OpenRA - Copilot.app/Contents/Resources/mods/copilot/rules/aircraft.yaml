BADR:
	Inherits: ^NeutralPlane
	ParaDrop:
		DropRange: 4c0
		ChuteSound: chute1.aud
	Health:
		HP: 40000
	Aircraft:
		CruiseAltitude: 2560
		TurnSpeed: 20
		Speed: 180
		Repulsable: False
		MaximumPitch: 56
	Cargo:
		MaxWeight: 10
	-Selectable:
	Interactable:
	-Voiced:
	Tooltip:
		Name: actor-badr-name
	Contrail@1:
		Offset: -432,560,0
		StartColorAlpha: 128
	Contrail@2:
		Offset: -432,-560,0
		StartColorAlpha: 128
	SpawnActorOnDeath:
		Actor: BADR.Husk
	LeavesTrails@0:
		Offsets: -432,560,0
		MovingInterval: 2
		Image: smokey
		SpawnAtLastPosition: False
		Type: CenterPosition
		RequiresCondition: enable-smoke
	LeavesTrails@1:
		Offsets: -432,-560,0
		MovingInterval: 2
		Image: smokey
		SpawnAtLastPosition: False
		Type: CenterPosition
		RequiresCondition: enable-smoke
	-EjectOnDeath:
	RejectsOrders:
	GivesExperience:
		Experience: 1000
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke

BADR.Bomber:
	Inherits: BADR
	-ParaDrop:
	-Cargo:
	-MapEditorData:
	RenderSprites:
		Image: badr
	AttackBomber:
		FacingTolerance: 8
	AmmoPool:
		Ammo: 10
	Armament:
		Weapon: ParaBomb

MIG:
	Inherits: ^Plane
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Aircraft
		BuildAtProductionType: Plane
		BuildPaletteOrder: 50
		Prerequisites: ~afld, stek, ~techlevel.high
		Description: actor-mig.description
	Valued:
		Cost: 2000
	Tooltip:
		Name: actor-mig.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 24000
	RevealsShroud:
		MinRange: 11c0
		Range: 13c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 11c0
		Type: GroundPosition
	Armament:
		Weapon: Maverick
		LocalOffset: 0,-640,0, 0,640,0
		LocalYaw: -40, 24
		PauseOnCondition: !ammo
	AttackAircraft:
		FacingTolerance: 80
		PersistentTargeting: false
		OpportunityFire: False
	Aircraft:
		CruiseAltitude: 2560
		TurnSpeed: 16
		Speed: 223
		RepulsionSpeed: 40
		MaximumPitch: 56
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: HoldFire
	AmmoPool:
		Ammo: 8
		AmmoCondition: ammo
	Selectable:
		Bounds: 1536, 1194, 0, 85
		DecorationBounds: 1706, 1237, 0, 42
	Contrail@1:
		Offset: -598,-683,0
		StartColorAlpha: 128
	Contrail@2:
		Offset: -598,683,0
		StartColorAlpha: 128
	SpawnActorOnDeath:
		Actor: MIG.Husk
	LeavesTrails:
		Offsets: -853,0,171
		MovingInterval: 2
		Image: smokey
		SpawnAtLastPosition: False
		Type: CenterPosition
		RequiresCondition: enable-smoke
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	Rearmable:
		RearmActors: afld, afld.ukraine
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded

YAK:
	Inherits: ^Plane
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Aircraft
		BuildAtProductionType: Plane
		BuildPaletteOrder: 30
		Prerequisites: ~afld, ~techlevel.medium
		Description: actor-yak.description
	Valued:
		Cost: 1350
	Tooltip:
		Name: actor-yak.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 18000
	RevealsShroud:
		MinRange: 9c0
		Range: 11c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 9c0
		Type: GroundPosition
	Armament:
		Weapon: ChainGun.Yak
		LocalOffset: 256,-213,0, 256,213,0
		MuzzleSequence: muzzle
		PauseOnCondition: !ammo
	AttackAircraft:
		FacingTolerance: 80
		PersistentTargeting: false
		OpportunityFire: False
	Aircraft:
		CruiseAltitude: 2560
		TurnSpeed: 16
		Speed: 178
		RepulsionSpeed: 40
		MaximumPitch: 56
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: HoldFire
	AmmoPool:
		Ammo: 18
		ReloadDelay: 11
		AmmoCondition: ammo
	WithMuzzleOverlay:
	Contrail:
		Offset: -853,0,0
		StartColorAlpha: 128
	SpawnActorOnDeath:
		Actor: YAK.Husk
	LeavesTrails:
		Offsets: -853,0,0
		MovingInterval: 2
		Image: smokey
		SpawnAtLastPosition: False
		Type: CenterPosition
		RequiresCondition: enable-smoke
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	Selectable:
		DecorationBounds: 1280, 1194, 0, 85
	Rearmable:
		RearmActors: afld, afld.ukraine
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 6
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded

TRAN:
	Inherits: ^Helicopter
	Inherits@CARGOPIPS: ^CargoPips
	Buildable:
		Queue: Aircraft
		BuildAtProductionType: Helicopter
		BuildPaletteOrder: 10
		Prerequisites: ~hpad, ~techlevel.medium
		Description: actor-tran.description
	Valued:
		Cost: 900
	Tooltip:
		Name: actor-tran.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 14000
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
		Type: GroundPosition
	Aircraft:
		TurnSpeed: 20
		Speed: 128
		AltitudeVelocity: 0c58
	WithIdleOverlay@ROTOR1AIR:
		Offset: 597,0,213
		Sequence: rotor
		RequiresCondition: airborne
	WithIdleOverlay@ROTOR1GROUND:
		Offset: 597,0,213
		Sequence: slow-rotor
		RequiresCondition: !airborne
	WithIdleOverlay@ROTOR2AIR:
		Offset: -597,0,341
		Sequence: rotor2
		RequiresCondition: airborne
	WithIdleOverlay@ROTOR2GROUND:
		Offset: -597,0,341
		Sequence: slow-rotor2
		RequiresCondition: !airborne
	Cargo:
		Types: Infantry
		MaxWeight: 8
		AfterUnloadDelay: 40
	SpawnActorOnDeath:
		Actor: TRAN.Husk
	Selectable:
		DecorationBounds: 1706, 1536

HELI:
	Inherits: ^Helicopter
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Buildable:
		Queue: Aircraft
		BuildAtProductionType: Helicopter
		BuildPaletteOrder: 40
		Prerequisites: ~hpad, atek, ~techlevel.high
		Description: actor-heli.description
	Valued:
		Cost: 2000
	Tooltip:
		Name: actor-heli.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 12000
	RevealsShroud:
		MinRange: 10c0
		Range: 12c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 10c0
		Type: GroundPosition
	Armament@PRIMARY:
		Weapon: HellfireAA
		LocalOffset: 0,-213,-85, 0,213,-85
		PauseOnCondition: !ammo
	Armament@SECONDARY:
		Weapon: HellfireAG
		LocalOffset: 0,213,-85, 0,-213,-85
		PauseOnCondition: !ammo
	AttackAircraft:
		FacingTolerance: 80
		PersistentTargeting: false
		AttackType: Hover
		OpportunityFire: False
	Aircraft:
		TurnSpeed: 16
		Speed: 149
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: HoldFire
	WithIdleOverlay@ROTORAIR:
		Offset: 0,0,85
		Sequence: rotor
		RequiresCondition: airborne
	WithIdleOverlay@ROTORGROUND:
		Offset: 0,0,85
		Sequence: slow-rotor
		RequiresCondition: !airborne
	AmmoPool:
		Ammo: 8
		AmmoCondition: ammo
	SpawnActorOnDeath:
		Actor: HELI.Husk
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		SpawnAtLastPosition: False
		Type: CenterPosition
		RequiresCondition: enable-smoke
		TrailWhileStationary: True
		StationaryInterval: 3
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	Selectable:
		DecorationBounds: 1536, 1194
	Rearmable:
		RearmActors: hpad
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded

HIND:
	Inherits: ^Helicopter
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Aircraft
		BuildAtProductionType: Helicopter
		BuildPaletteOrder: 20
		Prerequisites: ~disabled, ~hpad, ~techlevel.medium
		Description: actor-hind.description
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-hind.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 10000
	RevealsShroud:
		MinRange: 8c0
		Range: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	Armament:
		Weapon: ChainGun
		LocalOffset: 85,-213,-85, 85,213,-85
		MuzzleSequence: muzzle
		PauseOnCondition: !ammo
	AttackAircraft:
		FacingTolerance: 80
		PersistentTargeting: false
		AttackType: Hover
		OpportunityFire: False
	Aircraft:
		TurnSpeed: 16
		Speed: 112
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: HoldFire
	WithIdleOverlay@ROTORAIR:
		Sequence: rotor
		RequiresCondition: airborne
	WithIdleOverlay@ROTORGROUND:
		Sequence: slow-rotor
		RequiresCondition: !airborne
	AmmoPool:
		Ammo: 24
		ReloadDelay: 8
		AmmoCondition: ammo
	WithMuzzleOverlay:
	SpawnActorOnDeath:
		Actor: HIND.Husk
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		SpawnAtLastPosition: False
		Type: CenterPosition
		RequiresCondition: enable-smoke
		TrailWhileStationary: True
		StationaryInterval: 3
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	Selectable:
		DecorationBounds: 1621, 1365
	Rearmable:
		RearmActors: hpad
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 6
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded

U2:
	Inherits: ^NeutralPlane
	Health:
		HP: 200000
	Tooltip:
		Name: actor-u2-name
	Aircraft:
		CruiseAltitude: 2560
		TurnSpeed: 28
		Speed: 373
		Repulsable: False
		MaximumPitch: 56
	AttackBomber:
		FacingTolerance: 8
	-Selectable:
	-Voiced:
	-Targetable@AIRBORNE:
	Contrail@1:
		Offset: -725,683,0
		StartColorAlpha: 128
	Contrail@2:
		Offset: -725,-683,0
		StartColorAlpha: 128
	SpawnActorOnDeath:
		Actor: U2.Husk
	LeavesTrails:
		Offsets: -1c43,0,0
		MovingInterval: 2
		Image: smokey
		SpawnAtLastPosition: False
		Type: CenterPosition
		RequiresCondition: enable-smoke
	RejectsOrders:
	Interactable:
	-MapEditorData:
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke

MH60:
	Inherits: ^Helicopter
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Aircraft
		BuildAtProductionType: Helicopter
		BuildPaletteOrder: 20
		Prerequisites: ~hpad, ~techlevel.medium
		Description: actor-mh60.description
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-mh60.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 10000
	RevealsShroud:
		MinRange: 8c0
		Range: 10c0
		Type: GroundPosition
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 8c0
		Type: GroundPosition
	Armament:
		Weapon: ChainGun
		LocalOffset: 85,-213,-85, 85,213,-85
		MuzzleSequence: muzzle
		PauseOnCondition: !ammo
	AttackAircraft:
		FacingTolerance: 80
		PersistentTargeting: false
		AttackType: Hover
	Aircraft:
		TurnSpeed: 16
		Speed: 112
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: HoldFire
	WithIdleOverlay@ROTORAIR:
		Sequence: rotor
		RequiresCondition: airborne
	WithIdleOverlay@ROTORGROUND:
		Sequence: slow-rotor
		RequiresCondition: !airborne
	AmmoPool:
		Ammo: 24
		ReloadDelay: 8
		AmmoCondition: ammo
	WithMuzzleOverlay:
	SpawnActorOnDeath:
		Actor: MH60.Husk
	LeavesTrails:
		Offsets: -427,0,0
		Image: smokey
		MovingInterval: 3
		SpawnAtLastPosition: False
		Type: CenterPosition
		RequiresCondition: enable-smoke
		TrailWhileStationary: True
		StationaryInterval: 3
	ProducibleWithLevel:
		Prerequisites: aircraft.upgraded
	Selectable:
		DecorationBounds: 1621, 1365
	Rearmable:
		RearmActors: hpad
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 6
	GrantConditionOnDamageState@SmokeTrail:
		Condition: enable-smoke
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: aircraft.upgraded
