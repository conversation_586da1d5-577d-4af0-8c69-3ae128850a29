DOG:
	Inherits: ^Soldier
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Dog
		BuildPaletteOrder: 50
		Prerequisites: ~never-build
		Description: actor-dog.description
	Valued:
		Cost: 200
	Tooltip:
		Name: actor-dog.name
		GenericName: actor-dog.generic-name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Selectable:
		Bounds: 512, 725, -42, -170
		DecorationBounds: 512, 725, -42, -170
	Health:
		HP: 1800
	Mobile:
		Speed: 100
		Voice: Move
		PauseOnCondition: attack-cooldown || eating
	Guard:
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		Range: 5c512
	Armament:
		Weapon: DogJaw
		ReloadingCondition: attack-cooldown
	-AttackFrontal:
	AttackLeap:
		Voice: Attack
		PauseOnCondition: attacking || attack-cooldown
	AttackMove:
		Voice: Move
	GrantConditionOnAttack:
		Condition: eating
		RevokeDelay: 20
	GrantConditionWhileAiming:
		Condition: run
	AutoTarget:
		InitialStance: AttackAnything
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry
	Targetable:
		TargetTypes: GroundActor, Infantry
	WithInfantryBody:
		MoveSequence: walk
		StandSequences: stand
		DefaultAttackSequence: eat
		RequiresCondition: !run && !parachute
	WithInfantryBody@RUN:
		MoveSequence: run
		RequiresCondition: run && !parachute
	WithInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	SpeedMultiplier:
		Modifier: 150
		RequiresCondition: run
	IgnoresDisguise:
	Voiced:
		VoiceSet: DogVoice
	-TakeCover:

E1:
	Inherits: ^Soldier
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 10
		Prerequisites: ~barracks, ~techlevel.infonly
		Description: actor-e1.description
	Selectable:
		Class: E1
	Valued:
		Cost: 100
	Tooltip:
		Name: actor-e1.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 15000
	Armament@PRIMARY:
		Weapon: M1Carbine
	Armament@GARRISONED:
		Name: garrisoned
		Weapon: Vulcan
		MuzzleSequence: garrison-muzzle
	WithInfantryBody:
		DefaultAttackSequence: shoot
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		StandSequences: parachute
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded

E1R1:
	Inherits: E1
	RenderSprites:
		Image: E1
	ProducibleWithLevel:
		Prerequisites: techlevel.infonly
		InitialLevels: 1
	UpdatesPlayerStatistics:
		OverrideActor: e1
	-Buildable:
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: techlevel.infonly

E2:
	Inherits: ^Soldier
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 40
		Prerequisites: ~never-build
		Description: actor-e2.description
	Valued:
		Cost: 160
	Tooltip:
		Name: actor-e2.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 15000
	Mobile:
		Speed: 68
	Armament@PRIMARY:
		Weapon: Grenade
		LocalOffset: 0,0,555
		FireDelay: 15
	Armament@GARRISONED:
		Name: garrisoned
		Weapon: Grenade
		FireDelay: 15
	TakeCover:
		ProneOffset: 256,64,-331
	WithInfantryBody:
		DefaultAttackSequence: throw
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		StandSequences: parachute
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	Explodes:
		Weapon: UnitExplodeSmall
		EmptyWeapon: UnitExplodeSmall
		DamageSource: Killer
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded

E3:
	Inherits: ^Soldier
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 30
		Prerequisites: ~barracks, ~techlevel.infonly
		Description: actor-e3.description
	Selectable:
		Class: E3
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-e3.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 4500
	Armament@PRIMARY:
		Weapon: RedEye
		LocalOffset: 0,0,555
	Armament@SECONDARY:
		Name: secondary
		Weapon: Dragon
		LocalOffset: 0,0,555
	Armament@GARRISONED:
		Name: garrisoned
		Weapon: RedEye
	Armament@GARRISONEDSECONDARY:
		Name: garrisoned
		Weapon: Dragon
	TakeCover:
		ProneOffset: 384,0,-395
	WithInfantryBody:
		DefaultAttackSequence: shoot
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		StandSequences: parachute
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	AutoTarget:
		ScanRadius: 5
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded

E3R1:
	Inherits: E3
	RenderSprites:
		Image: E3
	ProducibleWithLevel:
		Prerequisites: techlevel.infonly
		InitialLevels: 1
	UpdatesPlayerStatistics:
		OverrideActor: e3
	-Buildable:
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: techlevel.infonly

E4:
	Inherits: ^Soldier
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 70
		Prerequisites: ~\1
		Description: actor-e4.description
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-e4.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 4000
	Armament@PRIMARY:
		Weapon: Flamer
		LocalOffset: 700,0,500
		FireDelay: 8
	Armament@GARRISONED:
		Name: garrisoned
		Weapon: Flamer
	TakeCover:
		ProneOffset: 160,0,-288
	Explodes:
		Weapon: VisualExplode
		EmptyWeapon: VisualExplode
		Chance: 50
	WithInfantryBody:
		DefaultAttackSequence: shoot
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		StandSequences: parachute
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded

E6:
	Inherits: ^Soldier
	Inherits@selection: ^SelectableSupportUnit
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 60
		Prerequisites: ~never-build
		Description: actor-e6.description
	Valued:
		Cost: 400
	Tooltip:
		Name: actor-e6.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	WithInfantryBody:
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		StandSequences: parachute
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	Passenger:
		CustomPipType: yellow
	InstantlyRepairs:
	RepairsBridges:
	CaptureManager:
	GrantConditionOnPrerequisite@GLOBALREUSABLEENGINEER:
		Condition: global-reusable-engineers
		Prerequisites: global-reusable-engineers
	Captures:
		RequiresCondition: !global-reusable-engineers
		CaptureTypes: building
		PlayerExperience: 10
		CaptureDelay: 200
	Captures@REUSABLE:
		RequiresCondition: global-reusable-engineers
		CaptureTypes: building
		PlayerExperience: 10
		CaptureDelay: 375
		ConsumedByCapture: False
		EnterCursor: ability
		EnterBlockedCursor: move-blocked
	Voiced:
		VoiceSet: EngineerVoice
	-AttackFrontal:

SPY:
	Inherits: ^Soldier
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 90
		Prerequisites: ~!infantry.england, dome, ~tent, ~techlevel.medium
		Description: actor-spy.description
	Valued:
		Cost: 500
	-Tooltip:
	DisguiseTooltip:
		Name: actor-spy.disguisetooltip-name
		GenericName: actor-spy.disguisetooltip-generic-name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	-Guard:
	Mobile:
		Voice: Move
	RevealsShroud:
		Range: 5c0
	Passenger:
		CustomPipType: blue
		Voice: Move
	Disguise:
		DisguisedCondition: disguise
	Infiltrates:
		Types: SpyInfiltrate
		Notification: BuildingInfiltrated
		TextNotification: notification-building-infiltrated
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: HoldFire
		ScanRadius: 5
	-WithInfantryBody:
	WithDisguisingInfantryBody:
		DefaultAttackSequence: shoot
		IdleSequences: idle1,idle2
		StandSequences: stand,stand2
		RequiresCondition: !parachute
	WithDisguisingInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	WithDecoration@disguise:
		Position: Top
		Margin: 0, -6
		Image: pips
		Sequence: tag-spy
		Palette: effect
		RequiresCondition: disguise
	IgnoresDisguise:
	Armament:
		Weapon: SilencedPPK
	AttackMove:
		Voice: Move
	Voiced:
		VoiceSet: SpyVoice

SPY.England:
	Inherits: SPY
	Buildable:
		Prerequisites: ~infantry.england, dome, ~tent, ~techlevel.medium
	Valued:
		Cost: 250
	GivesExperience:
		Experience: 500
	DisguiseTooltip:
		Name: actor-spy-england-disguisetooltip-name
	RenderSprites:
		Image: spy

E7:
	Inherits: ^Soldier
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 120
		Prerequisites: ~tent, atek, ~techlevel.high, ~never-build
		BuildLimit: 1
		Description: actor-e7.description
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-e7.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 10000
	Mobile:
		Speed: 68
		Voice: Move
	Guard:
		Voice: Move
	RevealsShroud:
		Range: 6c0
	Demolition:
		DetonationDelay: 45
		Voice: Demolish
	Passenger:
		CustomPipType: red
		Voice: Move
	Armament@PRIMARY:
		Weapon: Colt45
		LocalOffset: 0,0,0, 0,0,0
	Armament@GARRISONED:
		Name: garrisoned
		Weapon: Colt45
		MuzzleSequence: garrison-muzzle
	WithInfantryBody:
		AttackSequences:
			primary: shoot-left, shoot-right
		StandSequences: stand
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	ExternalCondition@PRODUCED:
		Condition: produced
	VoiceAnnouncement:
		RequiresCondition: produced
		Voice: Build
	AnnounceOnKill:
	Voiced:
		VoiceSet: TanyaVoice
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded

MEDI:
	Inherits: ^Soldier
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 40
		Prerequisites: ~tent, ~techlevel.infonly
		Description: actor-medi.description
	Valued:
		Cost: 200
	Tooltip:
		Name: actor-medi.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 6000
	Mobile:
		Speed: 49
	RevealsShroud:
		Range: 3c0
	Passenger:
		CustomPipType: blue
	Armament:
		Weapon: Heal
		Cursor: heal
		OutsideRangeCursor: heal
		TargetRelationships: Ally
		ForceTargetRelationships: None
	WithInfantryBody:
		IdleSequences: idle
		StandSequences: stand
		DefaultAttackSequence: heal
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	Voiced:
		VoiceSet: MedicVoice
	AutoTarget:
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry

MECH:
	Inherits: ^Soldier
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 100
		Prerequisites: ~tent, fix, ~techlevel.medium
		Description: actor-mech.description
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-mech.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 8000
	Mobile:
		Speed: 49
		Voice: Move
	RevealsShroud:
		Range: 3c0
	Passenger:
		CustomPipType: blue
		Voice: Move
	Armament:
		Weapon: Repair
		Cursor: repair
		OutsideRangeCursor: repair
		TargetRelationships: Ally
		ForceTargetRelationships: None
	AttackFrontal:
		Voice: Action
		FacingTolerance: 0
	CaptureManager:
	Captures:
		CaptureTypes: husk
		PlayerExperience: 10
	Infiltrates:
		Types: Husk
		ValidRelationships: Ally
		EnterCursor: goldwrench
	WithInfantryBody:
		IdleSequences: idle
		DefaultAttackSequence: repair
		StandSequences: stand
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	Voiced:
		VoiceSet: MechanicVoice
	AutoTarget:
	AutoTargetPriority@DEFAULT:
		ValidTargets: Vehicle, Ship

EINSTEIN:
	Inherits: ^CivInfantry
	-Wanders:
	Tooltip:
		Name: actor-einstein-name
	Mobile:
		Speed: 68
	Voiced:
		VoiceSet: EinsteinVoice

DELPHI:
	Inherits: ^CivInfantry
	-Wanders:
	Tooltip:
		Name: actor-delphi-name
	Mobile:
		Speed: 68

CHAN:
	Inherits: ^CivInfantry
	Valued:
		Cost: 500
	Selectable:
		Class: CHAN
	Tooltip:
		Name: actor-chan-name

GNRL:
	Inherits@1: ^CivInfantry
	Inherits@2: ^ArmedCivilian
	-Wanders:
	Tooltip:
		Name: actor-gnrl-name
	Selectable:
		Class: GNRL
	Mobile:
		Voice: Move
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Move
	Passenger:
		Voice: Move
	Guard:
		Voice: Move
	Voiced:
		VoiceSet: StavrosVoice
	-ScaredyCat:
	TakeCover:
		DamageModifiers:
			Prone50Percent: 50
		DamageTriggers: TriggerProne
	WithInfantryBody:
		IdleSequences: idle1
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true

THF:
	Inherits: ^Soldier
	Inherits@selection: ^SelectableSupportUnit
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 110
		Prerequisites: ~barr, dome, ~techlevel.medium, ~never-build
		Description: actor-thf.description
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-thf.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 5000
	RevealsShroud:
		Range: 5c0
	Passenger:
		CustomPipType: blue
	CaptureManager:
	Captures:
		CaptureTypes: vehicle, aircraft
		PlayerExperience: 10
	Infiltrates:
		Types: ThiefInfiltrate
		Notification: BuildingInfiltrated
		TextNotification: notification-building-infiltrated
	Voiced:
		VoiceSet: ThiefVoice
	-TakeCover:
	WithInfantryBody:
		IdleSequences: idle
		StandSequences: stand
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	Crushable:
		WarnProbability: 95
	Cloak:
		InitialDelay: 250
		CloakDelay: 120
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Move
		DetectionTypes: Cloak
		PauseOnCondition: cloak-force-disabled
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	Mobile:
		Voice: Move
		Speed: 68
	-AttackFrontal:

SHOK:
	Inherits: ^Soldier
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 130
		Prerequisites: ~barr, stek, tsla, ~infantry.russia, ~techlevel.high, ~never-build
		Description: actor-shok.description
	Valued:
		Cost: 350
	Tooltip:
		Name: actor-shok.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 5000
	Mobile:
		Voice: Move
	RevealsShroud:
		Range: 5c0
	Armament@PRIMARY:
		Weapon: PortaTesla
		LocalOffset: 427,0,341
	Armament@GARRISONED:
		Name: garrisoned
		Weapon: PortaTesla
	TakeCover:
		ProneOffset: 227,0,-245
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Move
	Passenger:
		Voice: Move
	Guard:
		Voice: Move
	WithInfantryBody:
		DefaultAttackSequence: shoot
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		StandSequences: parachute
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	Voiced:
		VoiceSet: ShokVoice
	ProducibleWithLevel:
		Prerequisites: barracks.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: barracks.upgraded

Zombie:
	Inherits: ^Soldier
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 100
	Tooltip:
		Name: actor-zombie.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 200
		Prerequisites: ~barracks, ~bio
		Description: actor-zombie.description
	Health:
		HP: 25000
	Mobile:
		Speed: 39
	AutoTarget:
		ScanRadius: 5
	WithInfantryBody:
		DefaultAttackSequence: bite
		IdleSequences: idle1
		RequiresCondition: !parachute
	WithInfantryBody@PARACHUTE:
		RequiresCondition: parachute
		Palette: player-noshadow
		IsPlayerPalette: true
	Armament:
		Weapon: claw
	Voiced:
		VoiceSet: AntVoice
	-TakeCover:

Ant:
	Inherits: ^Infantry
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-ant.name
		GenericName: actor-ant.generic-name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		Queue: Infantry
		BuildAtProductionType: Soldier
		BuildPaletteOrder: 1954
		Prerequisites: ~barracks, ~bio
		Description: actor-ant.description
	Selectable:
		Bounds: 1024, 1024, 0, -213
		DecorationBounds: 1280, 1280, 0, -85
	Health:
		HP: 75000
	Mobile:
		Speed: 92
		TurnSpeed: 48
		Locomotor: lighttracked
	-Crushable:
	AutoTarget:
		ScanRadius: 5
	AttackFrontal:
		FacingTolerance: 0
	WithInfantryBody:
		DefaultAttackSequence: bite
	Armament:
		Weapon: mandible
	Targetable:
		TargetTypes: GroundActor, Infantry, Ant
	WithDeathAnimation:
		UseDeathTypeSuffix: false
	Voiced:
		VoiceSet: AntVoice
	HitShape:
		Type: Circle
			Radius: 469

FireAnt:
	Inherits: Ant
	Tooltip:
		Name: actor-fireant-name
		GenericVisibility: none
	Mobile:
		Speed: 68
	Armament:
		Weapon: AntFireball
	Health:
		HP: 7500
	Armor:
		Type: Heavy
	Buildable:
		Prerequisites: ~disabled

ScoutAnt:
	Inherits: Ant
	Tooltip:
		Name: actor-scoutant-name
		GenericVisibility: none
	Health:
		HP: 8500
	Armor:
		Type: Light
	AutoTarget:
		ScanRadius: 7
	Buildable:
		Prerequisites: ~disabled

WarriorAnt:
	Inherits: Ant
	Tooltip:
		Name: actor-warriorant-name
		GenericVisibility: none
	Mobile:
		Speed: 56
	Health:
		HP: 12500
	Armor:
		Type: Heavy
	Armament:
		Weapon: MandibleHeavy
	Buildable:
		Prerequisites: ~disabled
	RenderSprites:
		Image: Ant
