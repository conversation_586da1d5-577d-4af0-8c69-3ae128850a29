Container@CHAT_PANEL:
	X: (WINDOW_RIGHT - WIDTH) / 2
	Y: WINDOW_BOTTOM - HEIGHT - 56
	Width: 550
	Height: 194
	Logic: IngameChatLogic
		OpenTeamChatKey: OpenTeamChat
		OpenGeneralChatKey: OpenGeneralChat
		Templates:
			Chat: CHAT_LINE_TEMPLATE
			System: SYSTEM_LINE_TEMPLATE
			Mission: CHAT_LINE_TEMPLATE
	Children:
		Container@CHAT_OVERLAY:
			Width: PARENT_RIGHT - 24
			Height: PARENT_BOTTOM - 30
			Visible: false
			Children:
				TextNotificationsDisplay@CHAT_DISPLAY:
					Width: PARENT_RIGHT
					Height: PARENT_BOTTOM
					DisplayDurationMs: 10000
					BottomSpacing: 3
		Container@CHAT_CHROME:
			Width: PARENT_RIGHT
			Height: PARENT_BOTTOM
			Children:
				Button@CHAT_MODE:
					Y: PARENT_BOTTOM - HEIGHT
					Width: 50
					Height: 25
					Text: button-chat-chrome-mode.label
					Font: Bold
					Key: ToggleChatMode
					TooltipText: button-chat-chrome-mode.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
				TextField@CHAT_TEXTFIELD:
					X: 55
					Y: PARENT_BOTTOM - HEIGHT
					Width: 466
					Height: 25
				Button@CHAT_CLOSE:
					X: 526
					Y: PARENT_BOTTOM - HEIGHT
					Width: 24
					Height: 25
					Children:
						Image:
							ImageCollection: lobby-bits
							ImageName: kick
							X: 6
							Y: 8
				LogicKeyListener@KEY_LISTENER:
				ScrollPanel@CHAT_SCROLLPANEL:
					Y: PARENT_BOTTOM - HEIGHT - 30
					Width: 550
					Height: 164
					TopBottomSpacing: 3
					ItemSpacing: 4
					Align: Bottom

