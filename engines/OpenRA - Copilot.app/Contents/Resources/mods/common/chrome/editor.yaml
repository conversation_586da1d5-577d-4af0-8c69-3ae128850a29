Background@NEW_MAP_BG:
	Logic: NewMapLogic
	X: (WINDOW_RIGHT - WIDTH) / 2
	Y: (WINDOW_BOTTOM - HEIGHT) / 2
	Width: 300
	Height: 185
	Children:
		Label@LABEL_TITLE:
			X: 0
			Y: 21
			Width: 300
			Height: 25
			Text: label-new-map-bg-title
			Align: Center
			Font: Bold
		Label@TILESET_LABEL:
			X: 20
			Y: 60
			Width: 95
			Height: 25
			Align: Right
			Text: label-new-map-bg-tileset
		DropDownButton@TILESET:
			X: 120
			Y: 60
			Width: 160
			Height: 25
		Label@WIDTH_LABEL:
			X: 20
			Y: 95
			Width: 95
			Height: 25
			Align: Right
			Text: label-new-map-bg-width
		TextField@WIDTH:
			X: 120
			Y: 95
			Width: 50
			MaxLength: 3
			Height: 25
			Text: 128
				Type: Integer
		Label@HEIGHT_LABEL:
			X: 130
			Y: 95
			Width: 95
			Height: 25
			Align: Right
			Text: label-new-map-bg-height
		TextField@HEIGHT:
			X: 230
			Y: 95
			Width: 50
			MaxLength: 3
			Height: 25
			Text: 128
			Type: Integer
		Button@CREATE_BUTTON:
			X: 30
			Y: PARENT_BOTTOM - 45
			Width: 120
			Height: 25
			Text: button-new-map-bg-create
			Font: Bold
			Key: return
		Button@CANCEL_BUTTON:
			X: 160
			Y: PARENT_BOTTOM - 45
			Width: 120
			Height: 25
			Text: button-cancel
			Font: Bold
			Key: escape

Background@SAVE_MAP_PANEL:
	Logic: SaveMapLogic
	X: (WINDOW_RIGHT - WIDTH) / 2
	Y: (WINDOW_BOTTOM - HEIGHT) / 2
	Width: 350
	Height: 300
	Children:
		Label@LABEL_TITLE:
			X: (PARENT_RIGHT - WIDTH) / 2
			Y: 21
			Width: 250
			Height: 25
			Text: label-save-map-panel-heading
			Align: Center
			Font: Bold
		Label@TITLE_LABEL:
			X: 10
			Y: 60
			Width: 95
			Height: 25
			Align: Right
			Text: label-save-map-panel-title
		TextField@TITLE:
			X: 110
			Y: 60
			Width: 220
			MaxLength: 50
			Height: 25
		Label@AUTHOR_LABEL:
			X: 10
			Y: 95
			Width: 95
			Height: 25
			Align: Right
			Text: label-save-map-panel-author
		TextField@AUTHOR:
			X: 110
			Y: 95
			Width: 220
			MaxLength: 50
			Height: 25
		Label@VISIBILITY_LABEL:
			X: 10
			Y: 130
			Width: 95
			Height: 25
			Align: Right
			Text: label-save-map-panel-visibility
		DropDownButton@VISIBILITY_DROPDOWN:
			X: 110
			Y: 130
			Width: 220
			Height: 25
			Text: dropdownbutton-save-map-panel-visibility-dropdown
		Label@DIRECTORY_LABEL:
			X: 10
			Y: 165
			Width: 95
			Height: 25
			Align: Right
			Text: label-save-map-panel-directory
		DropDownButton@DIRECTORY_DROPDOWN:
			X: 110
			Y: 165
			Width: 220
			Height: 25
		Label@FILENAME_LABEL:
			X: 10
			Y: 200
			Width: 95
			Height: 25
			Align: Right
			Text: label-save-map-panel-filename
		TextField@FILENAME:
			X: 110
			Y: 200
			Width: 105
			Height: 25
			Type: Filename
		DropDownButton@TYPE_DROPDOWN:
			X: 220
			Y: 200
			Width: 110
			Height: 25
		Button@SAVE_BUTTON:
			X: 80
			Y: PARENT_BOTTOM - 45
			Width: 120
			Height: 25
			Text: button-save-map-panel
			Font: Bold
		Button@BACK_BUTTON:
			X: 210
			Y: PARENT_BOTTOM - 45
			Width: 120
			Height: 25
			Text: button-cancel
			Font: Bold
			Key: escape

ScrollPanel@MAP_SAVE_VISIBILITY_PANEL:
	TopBottomSpacing: 5
	ItemSpacing: 5
	Width: 220
	Height: 55
	Children:
		Checkbox@VISIBILITY_TEMPLATE:
			X: 5
			Width: PARENT_RIGHT - 29
			Height: 20
			Font: Regular

Container@EDITOR_ROOT:
	Logic: LoadMapEditorLogic
	Children:
		LogicKeyListener@GLOBAL_KEYHANDLER:
			Logic: MusicHotkeyLogic, ScreenshotHotkeyLogic, MuteHotkeyLogic, EditorQuickSaveHotkeyLogic
				StopMusicKey: StopMusic
				PauseMusicKey: PauseMusic
				PrevMusicKey: PrevMusic
				NextMusicKey: NextMusic
				TakeScreenshotKey: TakeScreenshot
				MuteAudioKey: ToggleMute
				QuickSaveKey: EditorQuickSave
		LogicKeyListener@WORLD_KEYHANDLER:
			Logic: ResetZoomHotkeyLogic
				ResetZoomKey: ResetZoom
		Container@WORLD_ROOT:
		Container@MENU_ROOT:
		TooltipContainer@TOOLTIP_CONTAINER:

Container@EDITOR_WORLD_ROOT:
	Logic: LoadIngamePerfLogic, MapEditorLogic, ActorEditLogic, MapOverlaysLogic, MapEditorSelectionLogic
		ToggleGridOverlayKey: EditorToggleGridOverlay
		ToggleBuildableOverlayKey: EditorToggleBuildableOverlay
		ToggleMarkerOverlayKey: EditorToggleMarkerOverlay
	Children:
		LogicKeyListener@OVERLAY_KEYHANDLER:
		Container@PERF_ROOT:
		EditorViewportController@MAP_EDITOR:
			Width: WINDOW_RIGHT
			Height: WINDOW_BOTTOM
			TooltipContainer: TOOLTIP_CONTAINER
			TooltipTemplate: SIMPLE_TOOLTIP
		ViewportController:
			Width: WINDOW_RIGHT
			Height: WINDOW_BOTTOM
			IgnoreMouseOver: True
			ZoomInKey: ZoomIn
			ZoomOutKey: ZoomOut
			ScrollUpKey: MapScrollUp
			ScrollDownKey: MapScrollDown
			ScrollLeftKey: MapScrollLeft
			ScrollRightKey: MapScrollRight
			JumpToTopEdgeKey: MapJumpToTopEdge
			JumpToBottomEdgeKey: MapJumpToBottomEdge
			JumpToLeftEdgeKey: MapJumpToLeftEdge
			JumpToRightEdgeKey: MapJumpToRightEdge
			BookmarkSaveKeyPrefix: MapBookmarkSave
			BookmarkRestoreKeyPrefix: MapBookmarkRestore
			BookmarkKeyCount: 4
		Background@RADAR_BG:
			X: WINDOW_RIGHT - 325
			Y: 5
			Width: 320
			Height: 320
			Children:
				Radar@INGAME_RADAR:
					X: 10
					Y: 10
					Width: PARENT_RIGHT - 19
					Height: PARENT_BOTTOM - 19
		Background@TOOLS_BG:
			X: WINDOW_RIGHT - 320
			Y: 330
			Width: 310
			Height: WINDOW_BOTTOM - 422
		Container@TILE_WIDGETS:
			X: WINDOW_RIGHT - 320
			Y: 354
			Width: 310
			Height: WINDOW_BOTTOM - 458
			Logic: TileSelectorLogic
			Children:
				Label@SEARCH_LABEL:
					Y: 12
					Width: 55
					Height: 25
					Text: label-tiles-bg-search
					Align: Right
					Font: TinyBold
				TextField@SEARCH_TEXTFIELD:
					X: 60
					Y: 10
					Width: PARENT_RIGHT - 70
					Height: 25
				Label@CATEGORIES_LABEL:
					Y: 36
					Width: 55
					Height: 25
					Text: label-bg-filter
					Align: Right
					Font: TinyBold
				DropDownButton@CATEGORIES_DROPDOWN:
					X: 60
					Y: 34
					Width: PARENT_RIGHT - 70
					Height: 25
					Font: Bold
				ScrollPanel@TILETEMPLATE_LIST:
					X: 10
					Y: 58
					Width: PARENT_RIGHT - 20
					Height: PARENT_BOTTOM - 55
					TopBottomSpacing: 4
					ItemSpacing: 4
					Children:
						ScrollItem@TILEPREVIEW_TEMPLATE:
							Visible: false
							Width: PARENT_RIGHT - 35
							TooltipContainer: TOOLTIP_CONTAINER
							Children:
								TerrainTemplatePreview@TILE_PREVIEW:
									X: 4
									Y: 4
		Container@LAYER_WIDGETS:
			X: WINDOW_RIGHT - 320
			Y: 354
			Width: 310
			Height: WINDOW_BOTTOM - 458
			Visible: false
			Logic: LayerSelectorLogic
			Children:
				ScrollPanel@LAYERTEMPLATE_LIST:
					X: 10
					Y: 10
					Width: PARENT_RIGHT - 20
					Height: PARENT_BOTTOM - 7
					TopBottomSpacing: 4
					ItemSpacing: 4
					Children:
						ScrollItem@LAYERPREVIEW_TEMPLATE:
							Visible: false
							IgnoreChildMouseOver: true
							TooltipContainer: TOOLTIP_CONTAINER
							Children:
								ResourcePreview@LAYER_PREVIEW:
									X: 4
									Y: 4
									Visible: false
		Container@ACTOR_WIDGETS:
			X: WINDOW_RIGHT - 320
			Y: 354
			Width: 310
			Height: WINDOW_BOTTOM - 458
			Visible: false
			Logic: ActorSelectorLogic
			Children:
				Label@SEARCH_LABEL:
					Y: 12
					Width: 55
					Height: 25
					Text: label-actors-bg-search
					Align: Right
					Font: TinyBold
				TextField@SEARCH_TEXTFIELD:
					X: 60
					Y: 10
					Width: PARENT_RIGHT - 70
					Height: 25
				Label@CATEGORIES_LABEL:
					Y: 36
					Width: 55
					Height: 25
					Text: label-bg-filter
					Align: Right
					Font: TinyBold
				DropDownButton@CATEGORIES_DROPDOWN:
					X: 60
					Y: 34
					Width: PARENT_RIGHT - 70
					Height: 25
					Font: Bold
				Label@OWNERS_LABEL:
					Y: 60
					Width: 55
					Height: 25
					Text: label-actors-bg-owners
					Align: Right
					Font: TinyBold
				DropDownButton@OWNERS_DROPDOWN:
					X: 60
					Y: 58
					Width: PARENT_RIGHT - 70
					Height: 25
					Font: Bold
				ScrollPanel@ACTORTEMPLATE_LIST:
					X: 10
					Y: 82
					Width: PARENT_RIGHT - 20
					Height: PARENT_BOTTOM - 79
					TopBottomSpacing: 4
					ItemSpacing: 4
					Children:
						ScrollItem@ACTORPREVIEW_TEMPLATE:
							Visible: false
							Width: PARENT_RIGHT - 35
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
							IgnoreChildMouseOver: true
							Children:
								ActorPreview@ACTOR_PREVIEW:
									X: 4
									Y: 4
									Visible: true
		Container@TOOLS_WIDGETS:
			X: WINDOW_RIGHT - 320
			Y: 354
			Width: 310
			Height: WINDOW_BOTTOM - 458
			Visible: false
			Logic: MapToolsLogic
			Children:
				Label@TOOLS_LABEL:
					Y: 12
					Width: 55
					Height: 25
					Text: label-tools-bg-categories
					Align: Right
					Font: TinyBold
				DropDownButton@TOOLS_DROPDOWN:
					X: 60
					Y: 10
					Width: PARENT_RIGHT - 70
					Height: 25
					Font: Bold
				ScrollPanel@MARKER_TOOL_PANEL:
					X: 9
					Y: 35
					Width: 290
					Height: WINDOW_BOTTOM - 490
					Visible: true
					ScrollBar: Hidden
					ScrollbarWidth: 0
					Logic: MapMarkerTilesLogic
					Children:
						ScrollPanel@TILE_COLOR_PANEL:
							X: 6
							Y: 6
							Width: PARENT_RIGHT - 19
							Height: 31
							TopBottomSpacing: 1
							ItemSpacing: 1
							ScrollBar: Hidden
							ScrollbarWidth: 0
							ContentHeight: 31
							Children:
								ScrollItem@TILE_COLOR_TEMPLATE:
									Visible: false
									Height: 29
									Width: 29
									IgnoreChildMouseOver: true
									Children:
										ColorBlock@TILE_PREVIEW:
											X: 2
											Y: 2
											Width: 26
											Height: 26
								ScrollItem@TILE_ICON_TEMPLATE:
									Visible: false
									Height: 29
									Width: 29
									IgnoreChildMouseOver: true
									Children:
										Image@TILE_ERASE:
											X: 6
											Y: 6
											Width: 26
											Height: 26
											ImageCollection: editor
											ImageName: erase
						Button@CLEAR_CURRENT_BUTTON:
							X: 6
							Y: 42
							Width: 100
							Height: 25
							Text: button-marker-tiles-clear-current
							Font: Bold
						Button@CLEAR_ALL_BUTTON:
							X: 111
							Y: 42
							Width: 75
							Height: 25
							Text: button-marker-tiles-clear-all
							Font: Bold
						Label@ALPHA_LABEL:
							X: 6
							Y: 72
							Width: 265
							Height: 25
							Align: Left
							Text: label-marker-alpha
						Slider@ALPHA_SLIDER:
							X: 130
							Y: 72
							Width: 128
							Height: 25
						Label@ALPHA_VALUE:
							X: 260
							Y: 72
							Width: 30
							Height: 25
							Align: Left
						Label@MODE_LABEL:
							X: 6
							Y: 102
							Width: 265
							Height: 25
							Align: Left
							Text: label-marker-mirror-mode
						DropDownButton@MODE_DROPDOWN:
							X: 129
							Y: 102
							Width: 157
							Height: 25
						Label@NUM_SIDES_LABEL:
							X: 6
							Y: 132
							Width: 256
							Height: 25
							Align: Left
							Text: label-marker-layer-num-sides
						Slider@ROTATE_NUM_SIDES_SLIDER:
							X: 130
							Y: 132
							Width: 128
							Height: 25
							Visible: false
						Label@ROTATE_NUM_SIDES_VALUE:
							X: 260
							Y: 132
							Width: 30
							Height: 25
							Align: Left
							Visible: false
						DropDownButton@FLIP_NUM_SIDES_DROPDOWN:
							X: 129
							Y: 132
							Width: 157
							Height: 25
						Label@AXIS_ANGLE_LABEL:
							X: 6
							Y: 162
							Width: 256
							Height: 25
							Align: Left
							Text: label-marker-axis-angle
							Visible: false
						Slider@AXIS_ANGLE_SLIDER:
							X: 130
							Y: 162
							Width: 128
							Height: 25
							Visible: false
						Label@AXIS_ANGLE_VALUE:
							X: 260
							Y: 162
							Width: 30
							Height: 25
							Align: Left
							Visible: false
		Container@HISTORY_WIDGETS:
			X: WINDOW_RIGHT - 320
			Y: 354
			Width: 310
			Height: WINDOW_BOTTOM - 458
			Logic: HistoryLogLogic
			Visible: false
			Children:
				ScrollPanel@HISTORY_LIST:
					X: 10
					Y: 10
					Width: PARENT_RIGHT - 20
					Height: PARENT_BOTTOM - 7
					CollapseHiddenChildren: True
					TopBottomSpacing: 4
					ItemSpacing: 4
					Children:
						ScrollItem@HISTORY_TEMPLATE:
							X: 4
							Visible: false
							Width: PARENT_RIGHT - 31
							Height: 25
							IgnoreChildMouseOver: true
							TextColor: ffffff
							TextColorDisabled: 8f8f8f
							Children:
								Label@TITLE:
									X: 5
									Width: PARENT_RIGHT
									Height: 25
									Align: Left
		Container@SELECT_WIDGETS:
			X: WINDOW_RIGHT - 320
			Y: 354
			Width: 310
			Height: WINDOW_BOTTOM - 458
			Visible: false
			Children:
				Container@AREA_EDIT_PANEL:
					Width: 310
					Height: WINDOW_BOTTOM - 458
					Children:
						Label@AREA_EDIT_TITLE:
							X: 6
							Y: 16
							Width: 298
							Height: 24
							Align: Center
							Font: Bold
							Text: label-area-selection
						Label@AREA_FILTERS_LABEL:
							X: 15
							Y: 45
							Width: 150
							Height: 25
							Font: Bold
							Align: Left
							Text: label-copy-filters
						Checkbox@COPY_FILTER_TERRAIN_CHECKBOX:
							X: 15
							Y: 70
							Width: PARENT_RIGHT - 29
							Height: 20
							Text: label-filter-terrain
						Checkbox@COPY_FILTER_RESOURCES_CHECKBOX:
							X: 15
							Y: 95
							Width: PARENT_RIGHT - 29
							Height: 20
							Text: label-filter-resources
						Checkbox@COPY_FILTER_ACTORS_CHECKBOX:
							X: 15
							Y: 120
							Width: PARENT_RIGHT - 29
							Height: 20
							Text: label-filter-actors
						Label@AREA_INFO_TITLE:
							X: 6
							Y: 139
							Width: 298
							Height: 24
							Align: Center
							Font: Bold
							Text: label-area-info
						Label@DIAGONAL_LABEL:
							X: 15
							Y: 171
							Width: 120
							Height: 20
							Font: Bold
							Align: Left
							Text: label-selected-area-diagonal
						Label@RESOURCE_LABEL:
							X: 15
							Y: 193
							Width: 120
							Height: 25
							Font: Bold
							Align: Left
							Text: label-selected-area-resources
						Label@DIAGONAL_COUNTER_LABEL:
							X: 150
							Y: 170
							Width: 55
							Height: 22
							Align: Left
						Label@RESOURCES_COUNTER_LABEL:
							X: 150
							Y: 195
							Width: 55
							Height: 22
							Align: Left
						Button@SELECTION_CANCEL_BUTTON:
							X: 222
							Y: 222
							Width: 75
							Height: 25
							Text: button-cancel
							Font: Bold
				Container@ACTOR_EDIT_PANEL:
					Width: 310
					Height: WINDOW_BOTTOM - 458
					Children:
						Label@ACTOR_TYPE_LABEL:
							X: 15
							Y: 16
							Width: 281
							Height: 24
							Align: Center
							Font: Bold
						Label@ACTOR_ID_LABEL:
							X: 15
							Y: 45
							Width: 55
							Height: 25
							Text: label-actor-edit-panel-id
							Align: Right
						TextField@ACTOR_ID:
							X: 84
							Y: 45
							Width: 208
							Height: 25
						Label@ACTOR_ID_ERROR_LABEL:
							X: 84
							Y: 71
							Width: 208
							Height: 15
							Font: TinyBold
							TextColor: FF0000
						Container@ACTOR_INIT_CONTAINER:
							Y: 73
							Width: PARENT_RIGHT
							Children:
								Container@CHECKBOX_OPTION_TEMPLATE:
									Width: PARENT_RIGHT
									Height: 22
									Children:
										Checkbox@OPTION:
											X: 84
											Y: 1
											Width: PARENT_RIGHT - 100
											Height: 20
								Container@SLIDER_OPTION_TEMPLATE:
									Width: PARENT_RIGHT
									Height: 22
									Children:
										Label@LABEL:
											X: 15
											Y: 1
											Width: 55
											Height: 16
											Align: Right
										Slider@OPTION:
											X: 75
											Y: 1
											Width: 165
											Height: 20
										TextField@VALUE:
											X: 242
											Y: 1
											Width: 50
											Height: 20
											Type: Integer
								Container@DROPDOWN_OPTION_TEMPLATE:
									Width: PARENT_RIGHT
									Height: 27
									Children:
										Label@LABEL:
											X: 15
											Y: 2
											Width: 55
											Height: 24
											Align: Right
										DropDownButton@OPTION:
											X: 84
											Y: 1
											Width: 208
											Height: 25
											Font: Bold
						Container@BUTTON_CONTAINER:
							Y: 75
							Children:
								Button@DELETE_BUTTON:
									X: 15
									Width: 75
									Height: 25
									Text: button-container-delete
									Font: Bold
								Button@CANCEL_BUTTON:
									X: 142
									Width: 75
									Height: 25
									Text: button-cancel
									Font: Bold
								Button@OK_BUTTON:
									X: 222
									Width: 75
									Height: 25
									Text: button-container-ok
									Font: Bold
		Container@MAP_EDITOR_TAB_CONTAINER:
			Logic: MapEditorTabsLogic
			X: WINDOW_RIGHT - 311
			Y: 339
			Width: 292
			Height: 25
			Children:
				Button@SELECT_TAB:
					X: 0
					Width: 49
					Height: 25
					Key: EditorSelectTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-select-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 20
							Y: 5
							ImageCollection: editor
							ImageName: select
				Button@TILES_TAB:
					X: 49
					Width: 49
					Height: 25
					Key: EditorTilesTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-tiles-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 17
							Y: 5
							ImageCollection: editor
							ImageName: tiles
				Button@OVERLAYS_TAB:
					X: 98
					Width: 49
					Height: 25
					Key: EditorOverlaysTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-overlays-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 17
							Y: 5
							ImageCollection: editor
							ImageName: overlays
				Button@ACTORS_TAB:
					X: 147
					Width: 48
					Height: 25
					Key: EditorActorsTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-actors-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 16
							Y: 5
							ImageCollection: editor
							ImageName: actors
				Button@TOOLS_TAB:
					X: 195
					Width: 48
					Height: 25
					Key: EditorToolsTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-tools-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 16
							Y: 5
							ImageCollection: editor
							ImageName: tools
				Button@HISTORY_TAB:
					X: 243
					Width: 49
					Height: 25
					Key: EditorHistoryTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-history-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 17
							Y: 5
							ImageCollection: editor
							ImageName: history
		MenuButton@OPTIONS_BUTTON:
			Logic: MenuButtonsChromeLogic
			MenuContainer: INGAME_MENU
			HideIngameUI: true
			Pause: true
			Width: 60
			Height: 25
			Text: button-editor-world-root-options.label
			TooltipText: button-editor-world-root-options.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
			Font: Bold
			Key: escape
		Button@UNDO_BUTTON:
			X: 70
			Height: 25
			Width: 70
			Text: button-editor-world-root-undo.label
			Font: Bold
			Key: EditorUndo
			TooltipTemplate: BUTTON_TOOLTIP
			TooltipText: button-editor-world-root-undo.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
		Button@REDO_BUTTON:
			X: 150
			Height: 25
			Width: 70
			Text: button-editor-world-root-redo.label
			Font: Bold
			Key: EditorRedo
			TooltipTemplate: BUTTON_TOOLTIP
			TooltipText: button-editor-world-root-redo.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
		Button@COPY_BUTTON:
			X: 230
			Height: 25
			Text: button-editor-world-root-copy.label
			Width: 70
			Font: Bold
			Key: EditorCopy
			TooltipTemplate: BUTTON_TOOLTIP
			TooltipText: button-editor-world-root-copy.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
		Button@PASTE_BUTTON:
			X: 310
			Width: 70
			Height: 25
			Text: button-editor-world-root-paste.label
			Font: Bold
			Key: EditorPaste
			TooltipTemplate: BUTTON_TOOLTIP
			TooltipText: button-editor-world-root-paste.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
		DropDownButton@OVERLAY_BUTTON:
			X: 390
			Width: 140
			Height: 25
			Text: dropdownbutton-editor-world-root-overlay-button
			Font: Bold
		Label@COORDINATE_LABEL:
			X: 540
			Width: 50
			Height: 25
			Align: Left
			Font: Bold
			Contrast: true
		Label@CASH_LABEL:
			X: 655
			Width: 50
			Height: 25
			Align: Left
			Font: Bold
			Contrast: true

ScrollPanel@CATEGORY_FILTER_PANEL:
	Width: 240
	TopBottomSpacing: 5
	ItemSpacing: 5
	Children:
		Container@SELECT_CATEGORIES_BUTTONS:
			Width: PARENT_RIGHT
			Height: 25
			Children:
				Button@SELECT_ALL:
					X: 10
					Y: 0 - 5
					Width: 93
					Height: 25
					Text: button-select-categories-buttons-all
					Font: Bold
				Button@SELECT_NONE:
					X: 10 + 93 + 10
					Y: 0 - 5
					Width: 93
					Height: 25
					Text: button-select-categories-buttons-none
					Font: Bold
		Checkbox@CATEGORY_TEMPLATE:
			X: 5
			Width: PARENT_RIGHT - 29
			Height: 20
			Visible: false

ScrollPanel@OVERLAY_PANEL:
	Width: 140
	Height: 80
	ItemSpacing: 5
	TopBottomSpacing: 0
	Children:
		Checkbox@CATEGORY_TEMPLATE:
			X: 5
			Y: 5
			Width: PARENT_RIGHT - 29
			Height: 20
			Visible: false
