Background@ASSETBROWSER_PANEL:
	Logic: AssetBrowserLogic
	X: (WINDOW_RIGHT - WIDTH) / 2
	Y: (WINDOW_BOTTOM - HEIGHT) / 2
	Width: 900
	Height: 600
	Children:
		LogicTicker@ANIMATION_TICKER:
		Label@ASSETBROWSER_TITLE:
			Y: 16
			Width: PARENT_RIGHT
			Height: 25
			Font: Bold
			Align: Center
			Text: label-assetbrowser-title
		Label@SOURCE_SELECTOR_DESC:
			X: 20
			Y: 36
			Width: 195
			Height: 25
			Font: TinyBold
			Align: Center
			Text: label-assetbrowser-source-selector-desc
		DropDownButton@SOURCE_SELECTOR:
			X: 20
			Y: 60
			Width: 195
			Height: 25
			Font: Bold
			Text: dropdownbutton-assetbrowser-source-selector
		DropDownButton@ASSET_TYPES_DROPDOWN:
			X: 20
			Y: 90
			Width: 195
			Height: 25
			Font: Bold
			Text: dropdownbutton-assetbrowser-asset-types-dropdown
		Label@FILENAME_DESC:
			X: 20
			Y: 115
			Width: 195
			Height: 25
			Font: TinyBold
			Align: Center
			Text: label-assetbrowser-filename-desc
		<PERSON><PERSON><PERSON>@FILENAME_INPUT:
			X: 20
			Y: 140
			Width: 195
			Height: 25
			Type: Filename
		ScrollPanel@ASSET_LIST:
			X: 20
			Y: 170
			Width: 195
			Height: PARENT_BOTTOM - 250
			CollapseHiddenChildren: True
			Children:
				ScrollItem@ASSET_TEMPLATE:
					Width: PARENT_RIGHT - 27
					Height: 25
					X: 2
					Visible: false
					EnableChildMouseOver: True
					Children:
						LabelWithTooltip@TITLE:
							X: 10
							Width: PARENT_RIGHT - 20
							Height: 25
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
		Label@SPRITE_SCALE:
			X: PARENT_RIGHT - WIDTH - 440
			Y: 60
			Width: 50
			Height: 25
			Font: Bold
			Align: Left
			Text: label-assetbrowser-sprite-scale
		Slider@SPRITE_SCALE_SLIDER:
			X: PARENT_RIGHT - WIDTH - 330
			Y: 62
			Width: 100
			Height: 20
			MinimumValue: 0.5
			MaximumValue: 4
		Label@PALETTE_DESC:
			X: PARENT_RIGHT - WIDTH - 270
			Y: 60
			Width: 150
			Height: 25
			Font: Bold
			Align: Right
			Text: label-assetbrowser-palette-desc
		DropDownButton@PALETTE_SELECTOR:
			X: PARENT_RIGHT - WIDTH - 110
			Y: 60
			Width: 150
			Height: 25
			Font: Bold
		DropDownButton@COLOR:
			X: PARENT_RIGHT - WIDTH - 20
			Y: 60
			Width: 80
			Height: 25
			Children:
				ColorBlock@COLORBLOCK:
					X: 5
					Y: 6
					Width: PARENT_RIGHT - 35
					Height: PARENT_BOTTOM - 12
		Background@SPRITE_BG:
			X: 226
			Y: 90
			Width: PARENT_RIGHT - 226 - 20
			Height: PARENT_BOTTOM - 170
			Background: dialog3
			Children:
				Sprite@SPRITE:
					Width: PARENT_RIGHT
					Height: PARENT_BOTTOM
				VideoPlayer@PLAYER:
					Width: PARENT_RIGHT
					Height: PARENT_BOTTOM
					AspectRatio: 1
				Label@ERROR:
					Width: PARENT_RIGHT
					Height: PARENT_BOTTOM
					Align: Center
					Visible: false
					Text: label-assetbrowser-sprite-bg-error
		Container@FRAME_SELECTOR:
			X: 226
			Y: PARENT_BOTTOM - 75
			Width: PARENT_RIGHT - 226
			Children:
				Button@BUTTON_PREV:
					Width: 26
					Height: 26
					Key: LEFT
					Children:
						Image@IMAGE_PREV:
							X: 5
							Y: 5
							ImageCollection: music
							ImageName: prev
				Button@BUTTON_PLAY:
					X: 35
					Width: 26
					Height: 26
					Key: SPACE
					Children:
						Image@IMAGE_PLAY:
							X: 5
							Y: 5
							ImageCollection: music
							ImageName: play
				Button@BUTTON_PAUSE:
					Visible: false
					X: 35
					Width: 26
					Height: 26
					Key: SPACE
					Children:
						Image@IMAGE_PAUSE:
							X: 5
							Y: 5
							ImageCollection: music
							ImageName: pause
				Button@BUTTON_STOP:
					X: 70
					Width: 26
					Height: 26
					Key: RETURN
					Children:
						Image@IMAGE_STOP:
							X: 5
							Y: 5
							ImageCollection: music
							ImageName: stop
				Button@BUTTON_NEXT:
					X: 105
					Width: 26
					Height: 26
					Key: RIGHT
					Children:
						Image@IMAGE_NEXT:
							X: 5
							Y: 5
							ImageCollection: music
							ImageName: next
				Slider@FRAME_SLIDER:
					X: 140
					Y: 3
					Width: PARENT_RIGHT - 140 - 85
					Height: 20
					MinimumValue: 0
				Label@FRAME_COUNT:
					X: PARENT_RIGHT - WIDTH + 5
					Y: 0
					Width: 85
					Height: 25
					Font: TinyBold
					Align: Left
		Button@CLOSE_BUTTON:
			Key: escape
			X: PARENT_RIGHT - 180
			Y: PARENT_BOTTOM - 45
			Width: 160
			Height: 25
			Font: Bold
			Text: button-back
		TooltipContainer@TOOLTIP_CONTAINER:

ScrollPanel@ASSET_TYPES_PANEL:
	Width: 195
	Height: 130
	ItemSpacing: 5
	TopBottomSpacing: 0
	Children:
		Checkbox@ASSET_TYPE_TEMPLATE:
			X: 5
			Y: 5
			Width: PARENT_RIGHT - 29
			Height: 20

