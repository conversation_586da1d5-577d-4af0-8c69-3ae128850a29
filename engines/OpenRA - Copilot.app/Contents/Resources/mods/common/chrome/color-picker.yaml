Background@COLOR_CHOOSER:
	Logic: ColorPickerLogic
		PaletteColumns: 8
		PalettePresetRows: 2
		PaletteCustomRows: 1
	Background: dialog2
	Width: 326
	Height: 154
	Children:
		But<PERSON>@RANDOM_BUTTON:
			Key: tab
			X: 245
			Y: 95
			Width: 76
			Height: 25
			Text: button-color-chooser-random
			Font: Bold
		<PERSON><PERSON>@STORE_BUTTON:
			X: 245
			Y: 124
			Width: 76
			Height: 25
			Text: button-color-chooser-store
			Font: Bold
		ActorPreview@PREVIEW:
			X: 245
			Y: 13
			Width: 76
			Height: 73
		Button@MIXER_TAB_BUTTON:
			X: 5
			Y: PARENT_BOTTOM - 30
			Height: 25
			Width: 80
			Text: button-color-chooser-mixer-tab
			Font: Bold
		<PERSON><PERSON>@PALETTE_TAB_BUTTON:
			X: 85
			Y: PARENT_BOTTOM - 30
			Height: 25
			Width: 80
			Text: button-color-chooser-palette-tab
			Font: Bold
		Container@MIXER_TAB:
			X: 5
			Y: 5
			Width: PARENT_RIGHT - 90
			Height: PARENT_BOTTOM - 34
			Children:
				Background@HUEBG:
					Background: dialog3
					X: 0
					Y: 0
					Width: PARENT_RIGHT
					Height: 17
					Children:
						<PERSON><PERSON><PERSON><PERSON><PERSON>@HUE_SLIDER:
							X: 2
							Y: 2
							Width: PARENT_RIGHT - 4
							Height: PARENT_BOTTOM - 4
							Ticks: 5
				Background@MIXERBG:
					Background: dialog3
					X: 0
					Y: 22
					Width: PARENT_RIGHT
					Height: PARENT_BOTTOM - 22
					Children:
						ColorMixer@MIXER:
							X: 2
							Y: 2
							Width: PARENT_RIGHT - 4
							Height: PARENT_BOTTOM - 4
		Background@PALETTE_TAB:
			Background: dialog3
			X: 5
			Y: 5
			Width: PARENT_RIGHT - 90
			Height: PARENT_BOTTOM - 34
			Visible: false
			Children:
				Container@PALETTE_TAB_PANEL:
					X: 0
					Y: 0
					Width: PARENT_RIGHT
					Height: PARENT_BOTTOM
					Children:
						Background@PRESET_HEADER:
							Background: dialog2
							Width: PARENT_RIGHT - 4
							Height: 13
							X: 2
							Y: 2
							Children:
								Label@LABEL:
									Font: TinyBold
									Width: PARENT_RIGHT
									Height: 13
									Align: Center
									Text: label-preset-header
						Container@PRESET_AREA:
							Width: PARENT_RIGHT - 4
							Height: 58
							X: 2
							Y: 16
							Children:
								ColorBlock@COLORPRESET:
									X: 0
									Y: 0
									Width: 29
									Height: 29
									Visible: false
									ClickSound: ClickSound
						Background@CUSTOM_HEADER:
							Background: dialog2
							Width: PARENT_RIGHT - 4
							Height: 13
							X: 2
							Y: 75
							Children:
								Label@LABEL:
									Font: TinyBold
									Width: PARENT_RIGHT
									Height: 13
									Align: Center
									Text: label-custom-header
						Container@CUSTOM_AREA:
							Width: PARENT_RIGHT - 4
							Height: 31
							X: 2
							Y: 89
							Children:
								ColorBlock@COLORCUSTOM:
									X: 0
									Y: 0
									Width: 29
									Height: 29
									Visible: false
									ClickSound: ClickSound

