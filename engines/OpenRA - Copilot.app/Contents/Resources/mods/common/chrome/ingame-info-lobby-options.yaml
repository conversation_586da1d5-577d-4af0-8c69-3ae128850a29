Container@LOBBY_OPTIONS_PANEL:
	Height: PARENT_BOTTOM
	Width: PARENT_RIGHT
	Children:
		ScrollPanel:
			Logic: LobbyOptionsLogic
			X: 20
			Y: 20
			Width: PARENT_RIGHT - 40
			Height: 365
			Children:
				Container@LOBBY_OPTIONS:
					Y: 10
					Width: PARENT_RIGHT - 24
					Children:
						Container@CHECKBOX_ROW_TEMPLATE:
							Width: PARENT_RIGHT
							Height: 30
							Children:
								Checkbox@A:
									X: 10
									Width: PARENT_RIGHT / 2 - 20
									Height: 20
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								Checkbox@B:
									X: PARENT_RIGHT / 2 + 10
									Width: PARENT_RIGHT / 2 - 20
									Height: 20
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
						Container@DROPDOWN_ROW_TEMPLATE:
							Height: 60
							Width: PARENT_RIGHT
							Children:
								LabelForInput@A_DESC:
									X: 10
									Width: PARENT_RIGHT / 2 - 20
									Height: 20
									Visible: False
									For: A
								DropDownButton@A:
									X: 10
									Y: 25
									Width: PARENT_RIGHT / 2 - 20
									Height: 25
									Font: Regular
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								LabelForInput@B_DESC:
									X: PARENT_RIGHT / 2 + 10
									Width: PARENT_RIGHT / 2 - 20
									Height: 20
									Visible: False
									For: B
								DropDownButton@B:
									X: PARENT_RIGHT / 2 + 10
									Y: 25
									Width: PARENT_RIGHT / 2 - 20
									Height: 25
									Font: Regular
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER

