Metrics:
	ButtonDepth: 1
	ButtonFont: Regular
	ButtonTextColor: FFFFFF
	ButtonTextColorDisabled: 808080
	ButtonTextContrast: false
	ButtonTextContrastColorDark: 000000
	ButtonTextContrastColorLight: 7F7F7F
	ButtonTextContrastRadius: 1
	ButtonTextShadow: false
	GameStartedColor: FFA500
	HotkeyColor: FFFFFF
	HotkeyColorDisabled: 808080
	HotkeyColorInvalid: FF0000
	HotkeyFont: Regular
	IncompatibleGameColor: A9A9A9
	IncompatibleGameStartedColor: D2691E
	IncompatibleProtectedGameColor: 8B0000
	IncompatibleVersionColor: FF0000
	IncompatibleWaitingGameColor: 32CD32
	PlayerStanceColorAllies: FFFF00
	PlayerStanceColorEnemies: FF0000
	PlayerStanceColorNeutrals: D2B48C
	PlayerStanceColorSelf: 32CD32
	ProtectedGameColor: FF0000
	SpawnColor: FFFFFF
	SpawnContrastColor: 000000
	SpawnFont: TinyBold
	SpawnLabelOffset: 0,1
	TextColor: FFFFFF
	TextHighlightColor: FFFF00
	TextDisabledColor: 808080
	TextContrast: false
	TextContrastColorDark: 000000
	TextContrastColorLight: 7F7F7F
	TextContrastRadius: 1
	TextFont: Regular
	TextShadow: false
	TextfieldColor: FFFFFF
	TextfieldColorDisabled: 808080
	TextfieldColorInvalid: FFC0C0
	TextfieldColorHighlight: 195BC4
	TextfieldFont: Regular
	WaitingGameColor: 00FF00
	NoticeInfoColor: FFFFFF
	NoticeWarningColor: FFA500
	NoticeSuccessColor: 00FF00
	NoticeErrorColor: FF0000
	ChatLineSound: ChatLine
	PlayerJoinedSound: ChatLine
	LobbyOptionChangedSound: ChatLine
	PlayerLeftSound: ChatLine
	ClickDisabledSound: ClickDisabledSound
	ClickSound: ClickSound
	NormalSelectionColor: FFFFFF
	AltSelectionColor: 00FFFF
	CtrlSelectionColor: FFFF00
	ButtonCursor: default
	DefaultCursor: default
	WorldSelectCursor: select
	WorldDefaultCursor: default
