{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/osx-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/osx-x64": {"OpenRA.Utility/1.0.0": {"dependencies": {"OpenRA.Game": "1.0.0", "Roslynator.Analyzers": "4.2.0", "StyleCop.Analyzers": "1.2.0-beta.435", "runtimepack.Microsoft.NETCore.App.Runtime.osx-x64": "6.0.36"}, "runtime": {"OpenRA.Utility.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.osx-x64/6.0.36": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "11.100.3624.51421"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "6.0.3624.51421"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "6.0.3624.51421"}}, "native": {"createdump": {"fileVersion": "0.0.0.0"}, "libSystem.Globalization.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.IO.Compression.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Net.Security.Native.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.Apple.dylib": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.OpenSsl.dylib": {"fileVersion": "0.0.0.0"}, "libclrjit.dylib": {"fileVersion": "0.0.0.0"}, "libcoreclr.dylib": {"fileVersion": "0.0.0.0"}, "libdbgshim.dylib": {"fileVersion": "0.0.0.0"}, "libhostfxr.dylib": {"fileVersion": "0.0.0.0"}, "libhostpolicy.dylib": {"fileVersion": "0.0.0.0"}, "libmscordaccore.dylib": {"fileVersion": "0.0.0.0"}, "libmscordbi.dylib": {"fileVersion": "0.0.0.0"}}}, "Linguini.Bundle/0.6.0": {"dependencies": {"Linguini.Shared": "0.6.0", "Linguini.Syntax": "0.6.0"}, "runtime": {"lib/net6.0/Linguini.Bundle.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Linguini.Shared/0.6.0": {"runtime": {"lib/net6.0/Linguini.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Linguini.Syntax/0.6.0": {"dependencies": {"Linguini.Shared": "0.6.0"}, "runtime": {"lib/net6.0/Linguini.Syntax.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.DependencyModel/6.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Mono.Nat/3.0.4": {"runtime": {"lib/net6.0/Mono.Nat.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.4.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "OpenRA-Eluant/1.0.22": {"runtime": {"lib/netstandard2.0/Eluant.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Roslynator.Analyzers/4.2.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.unix.System.Private.Uri/4.3.0": {"dependencies": {"runtime.native.System": "4.3.0"}}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "StyleCop.Analyzers/1.2.0-beta.435": {"dependencies": {"StyleCop.Analyzers.Unstable": "1.2.0.435"}}, "StyleCop.Analyzers.Unstable/1.2.0.435": {}, "System.Buffers/4.5.1": {}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.unix.System.Private.Uri": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Threading.Channels/6.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "OpenRA.Game/1.0.0": {"dependencies": {"Linguini.Bundle": "0.6.0", "Microsoft.Extensions.DependencyModel": "6.0.0", "Mono.Nat": "3.0.4", "Newtonsoft.Json": "13.0.3", "OpenRA-Eluant": "1.0.22", "SharpZipLib": "1.4.2", "System.Runtime.Loader": "4.3.0", "System.Threading.Channels": "6.0.0"}, "runtime": {"OpenRA.Game.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"OpenRA.Utility/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.osx-x64/6.0.36": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Linguini.Bundle/0.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-9TT1sokmHx0o8uu6uTXzNa4DpPYaomZgqg9wDBoyMgofHBErELgHkYhXE0FsiJHSWw1kpUj6PVP5dnbG9wmTVA==", "path": "linguini.bundle/0.6.0", "hashPath": "linguini.bundle.0.6.0.nupkg.sha512"}, "Linguini.Shared/0.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-7128LQi6G7duUZ+b9ATgsGS2xBBLorXtPlENC4wc4ChhfMkUYVc7sjjpPYWa7LYYsa8scxEcfN75ZyLujyvzKA==", "path": "linguini.shared/0.6.0", "hashPath": "linguini.shared.0.6.0.nupkg.sha512"}, "Linguini.Syntax/0.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-cLRtJADeJl62ftgQACvxtWWy4CB8Yo2IC9USfRfdDaWAmQzPMrhqg38uemEUckVUnSuRlaSgql+Jeoy1k0DOFA==", "path": "linguini.syntax/0.6.0", "hashPath": "linguini.syntax.0.6.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TD5QHg98m3+QhgEV1YVoNMl5KtBw/4rjfxLHO0e/YV9bPUBDKntApP4xdrVtGgCeQZHVfC2EXIGsdpRNrr87Pg==", "path": "microsoft.extensions.dependencymodel/6.0.0", "hashPath": "microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Mono.Nat/3.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-oodXnwdcML4qUaZ+J44gaC/hn0n3uZHkvxScdt8NOcBbmbNmA7z1t5FEvUvn8cOnYSha8F4ZS57FJuXSKYhqdw==", "path": "mono.nat/3.0.4", "hashPath": "mono.nat.3.0.4.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OpenRA-Eluant/1.0.22": {"type": "package", "serviceable": true, "sha512": "sha512-sx+LNYp+MYQL+hSN1gOsLEqZK2nfftkQC62nnAYqmmPSWt5nYOZqm/U8lm8WsmGVllU3V8Y4W/bOZXMzux7l9g==", "path": "openra-eluant/1.0.22", "hashPath": "openra-eluant.1.0.22.nupkg.sha512"}, "Roslynator.Analyzers/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-3N8CNx1Q/Q5VDDL7qgfZRgTURyMqzHAkAB59AZKRnsOXoh2n9xRzhiBMIbJaUtBATmieECBx68GcjRn2xoNDug==", "path": "roslynator.analyzers/4.2.0", "hashPath": "roslynator.analyzers.4.2.0.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.unix.System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ooWzobr5RAq34r9uan1r/WPXJYG1XWy9KanrxNvEnBzbFdQbMG7Y3bVi4QxR7xZMNLOxLLTAyXvnSkfj5boZSg==", "path": "runtime.unix.system.private.uri/4.3.0", "hashPath": "runtime.unix.system.private.uri.4.3.0.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "StyleCop.Analyzers/1.2.0-beta.435": {"type": "package", "serviceable": true, "sha512": "sha512-TADk7vdGXtfTnYCV7GyleaaRTQjfoSfZXprQrVMm7cSJtJbFc1QIbWPyLvrgrfGdfHbGmUPvaN4ODKNxg2jgPQ==", "path": "stylecop.analyzers/1.2.0-beta.435", "hashPath": "stylecop.analyzers.1.2.0-beta.435.nupkg.sha512"}, "StyleCop.Analyzers.Unstable/1.2.0.435": {"type": "package", "serviceable": true, "sha512": "sha512-ouwPWZxbOV3SmCZxIRqHvljkSzkCyi1tDoMzQtDb/bRP8ctASV/iRJr+A2Gdj0QLaLmWnqTWDrH82/iP+X80Lg==", "path": "stylecop.analyzers.unstable/1.2.0.435", "hashPath": "stylecop.analyzers.unstable.1.2.0.435.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "OpenRA.Game/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}, "runtimes": {"osx-x64": ["osx", "unix-x64", "unix", "any", "base"], "osx.10.10-x64": ["osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.11-x64": ["osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.12-x64": ["osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.13-x64": ["osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.14-x64": ["osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.15-x64": ["osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.10.16-x64": ["osx.10.16", "osx.10.15-x64", "osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.11.0-x64": ["osx.11.0", "osx.10.16-x64", "osx.10.16", "osx.10.15-x64", "osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.12-x64": ["osx.12", "osx.11.0-x64", "osx.11.0", "osx.10.16-x64", "osx.10.16", "osx.10.15-x64", "osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"], "osx.13-x64": ["osx.13", "osx.12-x64", "osx.12", "osx.11.0-x64", "osx.11.0", "osx.10.16-x64", "osx.10.16", "osx.10.15-x64", "osx.10.15", "osx.10.14-x64", "osx.10.14", "osx.10.13-x64", "osx.10.13", "osx.10.12-x64", "osx.10.12", "osx.10.11-x64", "osx.10.11", "osx.10.10-x64", "osx.10.10", "osx-x64", "osx", "unix-x64", "unix", "any", "base"]}}