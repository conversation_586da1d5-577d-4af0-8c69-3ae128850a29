#!/bin/bash

# AI红警世界杯 2025 - 一键启动脚本
# 自动检测编译状态，没有编译就编译，然后运行

set -e

echo "🚀 AI红警世界杯 2025 - 一键启动"
echo "================================"

# 检查目录
if [ ! -f "copilot-source/Makefile" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

cd copilot-source

# 检查是否已编译
if [ ! -f "bin/OpenRA.dll" ]; then
    echo "🔨 检测到未编译，开始编译..."
    
    # 检查 .NET
    if command -v dotnet &> /dev/null; then
        echo "✅ 使用 .NET 编译"
        make
    elif command -v mono &> /dev/null; then
        echo "✅ 使用 Mono 编译"
        make RUNTIME=mono
    else
        echo "❌ 未找到 .NET 或 Mono，请先安装"
        exit 1
    fi
    
    echo "✅ 编译完成"
else
    echo "✅ 已编译，直接启动"
fi

# 设置环境变量并启动
export PATH="$HOME/.dotnet:$PATH"
echo "🎮 启动 Copilot mod..."
./launch-game.sh Game.Mod=copilot "$@"
